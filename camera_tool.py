import tkinter as tk
from tkinter import ttk
import cv2
from PIL import Image, ImageTk
import threading
import time

class BorderlessCameraTool:
    def __init__(self):
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("相机工具")
        
        # 设置窗口大小和位置
        self.window_width = 1980
        self.window_height = 1050
        self.root.geometry(f"{self.window_width}x{self.window_height}+-10+-10")
        
        # 设置无边框窗口
        self.root.overrideredirect(True)
        

        
        # 初始化相机相关变量
        self.cap = None
        self.is_camera_running = False
        self.current_frame = None

        # 图像处理参数
        self.rotation_angle = 180  # 默认旋转180度（倒置）
        self.is_mirrored = True  # 默认开启镜像翻转
        self.rotation_step = 5   # 每次旋转的角度步长
        
        # 创建界面
        self.create_widgets()
        
        # 绑定事件
        self.bind_events()
        
        # 尝试打开相机
        self.open_camera()
    
    def create_widgets(self):
        # 创建主框架
        self.main_frame = tk.Frame(self.root, bg='black')
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建顶部控制栏
        self.control_frame = tk.Frame(self.main_frame, bg='gray20', height=30)
        self.control_frame.pack(fill=tk.X, side=tk.TOP)
        self.control_frame.pack_propagate(False)
        
        # 关闭按钮
        self.close_btn = tk.Button(
            self.control_frame, 
            text="×", 
            bg='red', 
            fg='white',
            font=('Arial', 12, 'bold'),
            command=self.close_application,
            width=3,
            relief=tk.FLAT
        )
        self.close_btn.pack(side=tk.RIGHT, padx=5, pady=2)
        
        # 最小化按钮
        self.minimize_btn = tk.Button(
            self.control_frame, 
            text="—", 
            bg='gray40', 
            fg='white',
            font=('Arial', 12, 'bold'),
            command=self.minimize_window,
            width=3,
            relief=tk.FLAT
        )
        self.minimize_btn.pack(side=tk.RIGHT, padx=2, pady=2)
        
        # 标题标签
        self.title_label = tk.Label(
            self.control_frame, 
            text="相机工具", 
            bg='gray20', 
            fg='white',
            font=('Arial', 10)
        )
        self.title_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # 创建相机显示区域
        self.camera_frame = tk.Frame(self.main_frame, bg='black')
        self.camera_frame.pack(fill=tk.BOTH, expand=True)
        
        # 相机显示标签
        self.camera_label = tk.Label(
            self.camera_frame, 
            bg='black',
            text="正在启动相机...",
            fg='white',
            font=('Arial', 16)
        )
        self.camera_label.pack(fill=tk.BOTH, expand=True)
        
        # 底部状态栏
        self.status_frame = tk.Frame(self.main_frame, bg='gray20', height=25)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        self.status_frame.pack_propagate(False)
        
        # 状态标签
        self.status_label = tk.Label(
            self.status_frame, 
            text="状态: 初始化中...", 
            bg='gray20', 
            fg='white',
            font=('Arial', 9)
        )
        self.status_label.pack(side=tk.LEFT, padx=10, pady=2)
        
        # 分辨率显示
        self.resolution_label = tk.Label(
            self.status_frame,
            text="分辨率: 1980x1050",
            bg='gray20',
            fg='white',
            font=('Arial', 9)
        )
        self.resolution_label.pack(side=tk.RIGHT, padx=10, pady=2)

        # 控制按钮
        self.rotate_btn = tk.Button(
            self.status_frame,
            text="旋转",
            bg='gray40',
            fg='white',
            font=('Arial', 8),
            command=self.start_rotation,
            relief=tk.FLAT
        )
        self.rotate_btn.pack(side=tk.RIGHT, padx=5, pady=2)

        self.mirror_btn = tk.Button(
            self.status_frame,
            text="镜像",
            bg='gray40',
            fg='white',
            font=('Arial', 8),
            command=self.toggle_mirror,
            relief=tk.FLAT
        )
        self.mirror_btn.pack(side=tk.RIGHT, padx=5, pady=2)
    
    def bind_events(self):
        # 绑定拖拽事件
        self.control_frame.bind('<Button-1>', self.start_drag)
        self.control_frame.bind('<B1-Motion>', self.on_drag)
        self.title_label.bind('<Button-1>', self.start_drag)
        self.title_label.bind('<B1-Motion>', self.on_drag)
        
        # 绑定键盘事件
        self.root.bind('<Escape>', lambda e: self.close_application())
        self.root.focus_set()
    
    def start_drag(self, event):
        self.drag_start_x = event.x_root
        self.drag_start_y = event.y_root
        self.window_start_x = self.root.winfo_x()
        self.window_start_y = self.root.winfo_y()
    
    def on_drag(self, event):
        dx = event.x_root - self.drag_start_x
        dy = event.y_root - self.drag_start_y
        new_x = self.window_start_x + dx
        new_y = self.window_start_y + dy
        self.root.geometry(f"{self.window_width}x{self.window_height}+{new_x}+{new_y}")
    
    def minimize_window(self):
        self.root.iconify()
    
    def close_application(self):
        self.is_camera_running = False
        if self.cap:
            self.cap.release()
        self.root.quit()
        self.root.destroy()

    def start_rotation(self):
        """开始360度旋转动画"""
        start_angle = self.rotation_angle
        self.target_angle = start_angle + 360
        self.rotate_360()

    def rotate_360(self):
        """执行360度旋转动画"""
        if self.rotation_angle < self.target_angle:
            self.rotation_angle += self.rotation_step
            # 使用after方法在主线程中调度下一次旋转
            self.root.after(50, self.rotate_360)  # 50ms间隔，约20fps的旋转动画
        else:
            # 旋转完成后进行镜像翻转
            self.rotation_angle = self.target_angle % 360
            self.is_mirrored = not self.is_mirrored
            self.update_status()

    def toggle_mirror(self):
        """切换镜像状态"""
        self.is_mirrored = not self.is_mirrored
        self.update_status()

    def update_status(self):
        """更新状态显示"""
        mirror_status = "镜像" if self.is_mirrored else "正常"
        rotation_status = f"旋转{self.rotation_angle}°"
        self.status_label.config(text=f"状态: 相机运行中 - {mirror_status} - {rotation_status}")

    def apply_image_transforms(self, frame):
        """应用图像变换（旋转和镜像）"""
        # 应用旋转
        if self.rotation_angle != 0:
            height, width = frame.shape[:2]
            center = (width // 2, height // 2)
            rotation_matrix = cv2.getRotationMatrix2D(center, self.rotation_angle, 1.0)
            frame = cv2.warpAffine(frame, rotation_matrix, (width, height))

        # 应用镜像翻转
        if self.is_mirrored:
            frame = cv2.flip(frame, 1)  # 水平翻转

        return frame
    
    def open_camera(self):
        try:
            # 尝试打开默认相机
            self.cap = cv2.VideoCapture(0)
            
            if not self.cap.isOpened():
                self.status_label.config(text="状态: 无法打开相机")
                self.camera_label.config(text="无法打开相机\n请检查相机连接")
                return
            
            # 设置相机分辨率
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1920)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 1050)
            
            self.is_camera_running = True
            self.update_status()
            
            # 启动相机线程
            self.camera_thread = threading.Thread(target=self.update_camera, daemon=True)
            self.camera_thread.start()
            
        except Exception as e:
            self.status_label.config(text=f"状态: 错误 - {str(e)}")
            self.camera_label.config(text=f"相机错误:\n{str(e)}")
    
    def update_camera(self):
        while self.is_camera_running:
            try:
                ret, frame = self.cap.read()
                if ret:
                    # 调整帧大小以适应窗口
                    # 计算可用的显示区域高度（减去控制栏和状态栏）
                    available_height = self.window_height - 55  # 30(控制栏) + 25(状态栏)
                    
                    # 应用图像变换（旋转和镜像）
                    frame = self.apply_image_transforms(frame)

                    # 调整帧大小为1980x(available_height)
                    frame_resized = cv2.resize(frame, (self.window_width, available_height))

                    # 转换颜色格式
                    frame_rgb = cv2.cvtColor(frame_resized, cv2.COLOR_BGR2RGB)
                    
                    # 转换为PIL图像
                    pil_image = Image.fromarray(frame_rgb)
                    
                    # 转换为tkinter可用的格式
                    photo = ImageTk.PhotoImage(pil_image)
                    
                    # 更新显示
                    self.camera_label.config(image=photo, text="")
                    self.camera_label.image = photo  # 保持引用
                    
                else:
                    self.status_label.config(text="状态: 读取帧失败")
                    
            except Exception as e:
                self.status_label.config(text=f"状态: 更新错误 - {str(e)}")
                break
            
            time.sleep(0.033)  # 约30fps
    
    def run(self):
        self.root.mainloop()

def main():
    # 创建并运行相机工具
    camera_tool = BorderlessCameraTool()
    camera_tool.run()

if __name__ == "__main__":
    main()
