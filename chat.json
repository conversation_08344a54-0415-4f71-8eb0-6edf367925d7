{"77GHZ毫米波雷达调试与测试操作流程": ["77GHz长距离双波束ARS408-21毫米波雷达调试与测试操作流程\n步骤一：确保调试操作安全规范\n避免角反射器金属棱角刮伤毫米波雷达\n安全要求：\n对于相互传导连接的A级电压电路和B级电压电路，当电路中直流带电部件的以及与电平台连接，且其他任意一带电部分与这一极的最大电压不大于30VDC，且不大于60VDC\n技能要点：\n选择正确的调试工具：明确CAN分析仪、数字万用表、耐磨手套、示波器、护目镜、探针、调试相关上位机软件等\n步骤二：摆放角反射器或者金属板作为目标物\n技能要点：\n1.摆放距离为2-3m\n2.使用数显激光水平仪确保目标物在毫米波雷达正前方且在同一高度下\n3.确保毫米波雷达前无遮挡\n步骤三：正确连接线路与信号传输电路\n技能要点：\n1.使用CAN分析连接上位机与毫米波雷达\n2.检查CAN分析仪是否插接到位\n步骤四：上位机参数配置与目标读取\n技能要点：\n1.打开毫米波雷达上位机读取数据，配置参数，读取目标物信息\n2.配置参数包括：雷达基本配置中的雷达ID、检测目标类型、雷达功率、排序方式、雷达灵敏度；显示设置中显示ID、横向、纵向距离。\n3.精准读取角反射器ID、X、Y信息\n步骤五：帧原始数据获取与解析\n技能要点：\n1.使用USB_CAN_TOOL上位机获取帧原始数据，首先依据CAN协议选择径向距离数值，并将其转化为二进制，其次按照CAN协议进行筛选剔除，再次将二进制数据转化为十进制，最后乘以系数加上偏移，得到我们的径向距离，解析目标物对应的帧原始数据\n步骤六：完成毫米波雷达的调试后请把物品分区、整理好物品、清除垃圾和脏污、维持成果、自觉遵守纪律\n技能要点：\n1.记录完整操作并规范填写工单\n2.关闭所有上位机软件\n3.断开CAN分析仪等外部连接设备\n\n以上生成内容参考：\n《GB/T18384—2020电动汽车安全要求》《T/CAAMTB15-2020车载毫米波雷达测试方法》、智能网联汽车实车实训系统使用手册、ARS_408-21毫米波雷达规格书、USBCAN(CANalyst-II分析仪)产品说明书、《车载毫米波雷达（77GHz）校准规范》、国际标准如ETSIS77GHzRSR雷达测试标准、《GB/T18384—2020电动汽车安全要求》"], "32线激光雷达调试与测试操作流程": ["镭神智能32线机械式激光雷达调试与测试操作流程\n步骤一：确保调试操作安全规范\n安全要求：\n在进行激光雷达的测试时，操作人员应穿戴适当的个人防护装备，如手套，以防止意外伤害。定期校准激光雷达，确保其功率和扫描精度符合标准。非专业人员不得拆解激光雷达，维修需由认证机构进行。\n技能要点：\n选择正确的调试工具：数字万用表、耐磨手套、护目镜、探针、调试相关上位机软件、网线测试仪、网络分析软件等\n步骤二：设置假人或者雪糕桶作为目标物\n技能要点：\n1.安全距离控制：测试目标距离雷达基准点≥4m，确保探测范围覆盖有效感知区。\n2.抗干扰管理：工作环境电磁场强度≤3V/m（频段30MHz-1GHz），排除电机/变频器等干扰源。\n步骤三：电气连接\n连接激光雷达硬件与通信接口，正确连接电源、以太网通信线路，确保信号传输稳定。连接32线激光雷达接线盒的以太网接口，连接上位机的以太网接口，并检查各接口的物理稳固性。\n技能要点：\n1.直流电源输出12V±0.5V（纹波≤100mV）\n2.以太网线采用Cat5e及以上屏蔽双绞线\n步骤四：调置参数\n调试参数与数据读取。使用激光雷达上位机程序解析点云数据与性能验证，实时读取点云数据，验证目标探测的准确性。\n技能要点：\n1.在设置网络属性中，配置IPV4以太网：*************，子网掩码：*************；32线激光雷达的设备端口号：2369，数据端口号：2368；左侧16线激光雷达的设备端口号：2367，数据端口号：2366；右侧16线激光雷达的设备端口号：2370，数据端口号：2371。\n2.实时性要求：点云输出延迟≤100ms\n3.精度校准：假人轮廓点云间距误差≤±3cm\n4.软件版本管理：上位机软件版本≥V2.1.3\n步骤五：获取目标物的三维坐标\n在点云图像中找到假人，框选假人的头部，记录获取到的三维坐标。\n技能要点：\n1.数据记录格式：输出为(N,X,Y,Z,Intensity)结构化表格\n步骤六：激光雷达标定\n导入ivd文件到激光建图，生成GPS、NDT数据。通过手册上的公式计算得到四元数\n技能要点：\n1.NDT数据输出：数据包需含UTC时间戳、WGS-84坐标系、置信度≥95%，点云可靠性要求\n\n以上内容参考：\n《GB/T45500-2025车载激光雷达性能要求及试验方法》、《T/CSAE车路协同路侧基础设施第4部分：激光雷达技术要求及测试方法》、《GB/T45312-2025智能网联汽车自动驾驶系统设计运行条件》、《GB/T26773-2011智能运输系统毫米波雷达性能要求及测试方法》、《T/CSAE智能网联汽车激光雷达感知评测要求及方法》中国国家标准（基于ISO15830系列）、GB/T 10320-2011《激光设备和设施的电气安全》、《GB/T26773-2011》、Q/ABC002-2025、ISO/IEC11801标准等。"], "多传感器联合标定调试与测试操作流程": ["多传感器联合标定调试与测试操作流程\n步骤一：检查各传感器的物理指示灯，在命令窗口执行数据流输出指令，判断传感器通信是否正常\n技能要点：\n1.进入工具箱中传感器状态一栏\n2.绑定车辆VIN码\n3.连接车辆局域网\n步骤二：使用卷尺测量标定区域并在角点摆放锥桶\n技能要点：\n使用卷尺（精度±1mm）测量确保宽度/长度误差≤±1cm\n步骤三：在标定画面中获取四个标定点的像素坐标，点击\"q\"开始标定，标定完成后将像素坐标值和实际测量的三维坐标值填写到对应参数文件中\n技能要点：\n1.保存的图像需无运动模糊、无过曝/欠曝，信噪比（SNR）≥30dB\n2.获取像素坐标时，确保鼠标点击位置在四个角点上\n步骤四：摄像头标定完成后，需要验证可视化是否达到预期效果。\n技能要点：\n摄像头光轴偏差≤±2mm\n\n以上内容参考行业标准，国家标准：\n《GB/T38606-2020物联网标识体系—数据内容标识符》、《GB/T38606-2020》、《GB/T32960.2-2016电动汽车远程服务与管理系统技术规范》、《T/CSAE183-2022》、《Q/JT202-2023自动驾驶传感器安装公差》、《T/CSAE183-2022》、ISO16505:2019《道路车辆-环视系统性能要求》、满足《GB/T45312-2025智能网联汽车自动驾驶系统设计运行条件》"], "毫米波雷达上位机无数据": ["毫米波雷达上位机无数据的故障现象，根据故障库中的检索结果，以下是诊断方法：\n\n第一步：查看计算平台是否给毫米波雷达供电。\n维修建议：确保车辆上电后开启计算平台12V电源给毫米波雷达供电，检查设备管理器的识别状态\n\n第二步：使用CAN分析仪连接上位机，检查设备管理器的识别状态，通常可在列表首行检测到设备存在\n维修建议：安装毫米波雷达驱动程序，更换CAN分析仪设备，进一步测量CAN分析仪的CAN_H和CAN_L的对地电压\n\n第三步：使用万用表测量CAN分析仪CAN_H和CAN_L的对地电压，正常应该有电压差。\n维修建议：进一步测量毫米波DB9插头2号与7号针脚对地电压\n\n第四步：使用万用表测量CAN分析仪的DB9插头2号与7号针脚对地电压，正常应该有电压差。\n维修建议：使用示波器进行进一步查看毫米波插头2号与7号针脚的波形\n\n第五步：使用示波器查看毫米波雷达插头2号与7号针脚的波形\n维修建议：计算平台12V电源关闭，打开CAN分析仪DB9插头盖子进行维修，更换DB9插头\n\n第六步：再次打开大陆雷达软件查看毫米波雷达是否有输出。\n通过以上步骤，可以针对每个硬件故障点快速定位和解决问题。"], "激光雷达上位机无数据": ["（LSC32机械式）激光雷达上位机无数据的故障现象，根据故障库中的检索结果，以下是可能的故障原因：\n第一种：32线激光雷达接线盒供电指示灯异常\n解决办法：检查供电线，若供电线异常，则更换供电线，若供电线正常，则检查保险丝，若保险丝正常，则更换激光雷达接线盒；若保险丝异常，则更换供电保险。\n第二种：以太网线通信异常\n解决办法：网线测试仪检查网线，若损坏则更换网线。\n第三种： 激光雷达目标数据端口和设备端口与工艺文件不匹配\n解决办法：使用Wireshark捕获激光雷达数据流，读取其实际使用的目标端口号，对照工艺文件。若异常，则在激光雷达设置界面中，将上位机软件监听数据端口、上位机软件连接设备端口临时修改修改为Wireshark读取到的雷达数据端口、雷达设备端口。接着，在激光雷达设置界面，根据工艺文件将激光雷达数据端口和设备端口设置为2368和2369，重启上位机软件。\n第四种：激光雷达所需的上位机目标IPV4地址配置异常\n解决办法：若配置异常，则根据工艺文件，将上位机IPV4地址和子网掩码分别配置为*************和*************。"], "你好": ["你好，我是AI流程生成助手"]}