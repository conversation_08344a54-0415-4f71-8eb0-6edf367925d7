import os
import sys
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QPushButton, QMessageBox
from PyQt5.QtCore import Qt, QUrl, QTimer
from PyQt5.QtGui import QFont

# 尝试导入QWebEngineView，如果失败则使用备用方案
try:
    from PyQt5.QtWebEngineWidgets import QWebEngineView
    WEB_ENGINE_AVAILABLE = True
except ImportError:
    WEB_ENGINE_AVAILABLE = False
    QWebEngineView = None

class CommunityWidget(QWidget):
    """社区页面组件，显示指定的网页"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.community_url = "http://8.140.193.215:5000"
        self.web_view = None
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setStyleSheet(f"QWidget {{ background-color: #3a7bd9; }}")
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(0)

        # 创建网页显示区域
        self.create_web_view(main_layout)
    
    def create_web_view(self, parent_layout):
        """创建网页显示区域"""
        # 创建网页容器
        web_container = QFrame()
        web_container.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border-radius: 8px;
                border: 1px solid rgba(255, 255, 255, 0.3);
            }}
        """)

        web_layout = QVBoxLayout(web_container)
        web_layout.setContentsMargins(0, 0, 0, 0)
        
        if WEB_ENGINE_AVAILABLE and QWebEngineView:
            # 使用QWebEngineView显示网页
            self.web_view = QWebEngineView()
            self.web_view.setUrl(QUrl(self.community_url))

            # 连接加载完成信号
            self.web_view.loadFinished.connect(self.on_load_finished)

            web_layout.addWidget(self.web_view)
        else:
            # 备用方案：显示提示信息和打开外部浏览器的按钮
            fallback_layout = QVBoxLayout()
            fallback_layout.setAlignment(Qt.AlignCenter)
            fallback_layout.setSpacing(20)
            
            # 提示信息
            info_label = QLabel("无法在应用内显示网页")
            info_label.setAlignment(Qt.AlignCenter)
            info_label.setStyleSheet("""
                QLabel {
                    font-size: 18px;
                    color: #666666;
                    margin: 20px;
                }
            """)
            fallback_layout.addWidget(info_label)
            
            # 网址显示
            url_label = QLabel(f"社区地址：{self.community_url}")
            url_label.setAlignment(Qt.AlignCenter)
            url_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #888888;
                    margin: 10px;
                }
            """)
            fallback_layout.addWidget(url_label)
            
            # 打开外部浏览器按钮
            open_browser_btn = QPushButton("在浏览器中打开")
            open_browser_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: #1C64F2;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 24px;
                    font-size: 16px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: #165CE3;
                }}
                QPushButton:pressed {{
                    background-color: #1a5bb8;
                }}
            """)
            open_browser_btn.clicked.connect(lambda: self.open_url_in_browser())
            fallback_layout.addWidget(open_browser_btn)
            
            # 添加弹性空间
            fallback_layout.addStretch()
            
            web_layout.addLayout(fallback_layout)
        
        parent_layout.addWidget(web_container)
    
    def on_load_finished(self, success):
        """网页加载完成回调"""
        if not success:
            QMessageBox.warning(self, "加载失败", "无法加载社区页面，请检查网络连接。")

    def open_url_in_browser(self):
        """在外部浏览器中打开社区页面"""
        import webbrowser
        try:
            webbrowser.open(self.community_url)
        except Exception as e:
            QMessageBox.warning(self, "打开失败", f"无法打开浏览器：{str(e)}")

    def get_current_url(self):
        """获取当前URL"""
        if self.web_view and WEB_ENGINE_AVAILABLE:
            return self.web_view.url().toString()
        return self.community_url
