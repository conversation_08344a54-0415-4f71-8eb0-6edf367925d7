#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from PIL import Image, ImageDraw, ImageFont

def create_test_image(filename, text, size=(400, 300)):
    """创建测试图片"""
    # 创建图片
    img = Image.new('RGB', size, color='lightblue')
    draw = ImageDraw.Draw(img)
    
    # 尝试使用系统字体
    try:
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        try:
            font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 24)
        except:
            font = ImageFont.load_default()
    
    # 计算文本位置
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    x = (size[0] - text_width) // 2
    y = (size[1] - text_height) // 2
    
    # 绘制文本
    draw.text((x, y), text, fill='black', font=font)
    
    # 保存图片
    img.save(filename)
    print(f"创建测试图片: {filename}")

def main():
    # 确保image目录存在
    image_dir = "../image"
    if not os.path.exists(image_dir):
        os.makedirs(image_dir)
    
    # 创建测试图片
    test_images = [
        ("step1.png", "步骤1\n确保调试操作安全规范"),
        ("step2.png", "步骤2\n摆放角反射器或金属板"),
        ("step3.png", "步骤3\n正确连接线路与信号传输"),
        ("step4.png", "步骤4\n上位机参数配置与目标读取"),
        ("step5.png", "步骤5\n帧原始数据获取与解析"),
        ("step6.png", "步骤6\n完成调试后的整理工作"),
        ("lidar1.png", "激光雷达步骤1\n确保调试操作安全规范"),
        ("lidar2.png", "激光雷达步骤2\n设置假人或雪糕桶作为目标物"),
        ("lidar3.png", "激光雷达步骤3\n电气连接"),
        ("lidar4.png", "激光雷达步骤4\n调置参数"),
        ("lidar5.png", "激光雷达步骤5\n获取目标物的三维坐标"),
        ("lidar6.png", "激光雷达步骤6\n激光雷达标定"),
        ("camera1.png", "摄像头步骤1\n检查各传感器的物理指示灯"),
        ("camera2.png", "摄像头步骤2\n测量标定区域并摆放锥桶"),
        ("camera3.png", "摄像头步骤3\n获取标定点像素坐标"),
        ("camera4.png", "摄像头步骤4\n验证标定效果"),
    ]
    
    for filename, text in test_images:
        filepath = os.path.join(image_dir, filename)
        create_test_image(filepath, text)
    
    print("所有测试图片创建完成！")

if __name__ == "__main__":
    main()
