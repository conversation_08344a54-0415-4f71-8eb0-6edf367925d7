from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                           QLineEdit, QPushButton, QFormLayout, QFrame,
                           QWidget, QMessageBox)
from PyQt5.QtCore import Qt
import os
import paramiko
import time

class DeployDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("OTA部署设置")
        self.setMinimumWidth(600)
        self.setStyleSheet("background-color: white;")
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # 标题
        title = QLabel("远程设备配置")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px 0;
            }
        """)
        layout.addWidget(title, alignment=Qt.AlignCenter)
        
        # 创建表单
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e0e0e0;
            }
        """)
        form_layout = QFormLayout(form_frame)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(20, 20, 20, 20)
        
        # SSH连接配置
        self.target_ip = QLineEdit()
        self.target_ip.setPlaceholderText("例如：*************")
        self.username = QLineEdit()
        self.username.setPlaceholderText("请输入SSH用户名")
        self.password = QLineEdit()
        self.password.setPlaceholderText("请输入SSH密码")
        self.password.setEchoMode(QLineEdit.Password)
        
        # 网络配置（隐藏的默认值）
        self.vehicle_ip = QLineEdit("127.0.0.1")
        self.vehicle_ip.hide()
        self.vehicle_port = QLineEdit("5488")
        self.vehicle_port.hide()
        
        # 其他配置
        self.vehicle_vin = QLineEdit()
        self.vehicle_vin.setPlaceholderText("你的车辆VIN码")
        
        # 中间激光雷达配置
        self.mid_lidar_ip = QLineEdit()
        self.mid_lidar_ip.setPlaceholderText("*************")
        
        self.mid_lidar_port = QLineEdit()
        self.mid_lidar_port.setPlaceholderText("2368")
        
        # 左激光雷达配置
        self.left_lidar_ip = QLineEdit()
        self.left_lidar_ip.setPlaceholderText("*************")
        
        self.left_lidar_port = QLineEdit()
        self.left_lidar_port.setPlaceholderText("2366")
        
        # 右激光雷达配置
        self.right_lidar_ip = QLineEdit()
        self.right_lidar_ip.setPlaceholderText("*************")
        
        self.right_lidar_port = QLineEdit()
        self.right_lidar_port.setPlaceholderText("2370")
        
        # CAN配置
        self.mmwave_channel = QLineEdit()
        self.mmwave_channel.setPlaceholderText("can0")
        
        self.chassis_channel = QLineEdit()
        self.chassis_channel.setPlaceholderText("can1")
        
        # 摄像头配置
        self.camera_device = QLineEdit()
        self.camera_device.setPlaceholderText("/dev/video0")
        
        # 组合惯导配置
        self.gnss_channel = QLineEdit()
        self.gnss_channel.setPlaceholderText("/dev/ttyUART_232_C")
        
        self.gnss_baudrate = QLineEdit()
        self.gnss_baudrate.setPlaceholderText("230400")
        
        # 设置输入框样式
        input_style = """
            QLineEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
                min-width: 180px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
            QLineEdit::placeholder {
                color: #95a5a6;
                font-style: italic;
            }
        """
        for widget in [self.target_ip, self.username, self.password,
                      self.vehicle_vin, self.mid_lidar_ip, self.mid_lidar_port,
                      self.left_lidar_ip, self.left_lidar_port,
                      self.right_lidar_ip, self.right_lidar_port,
                      self.mmwave_channel, self.chassis_channel,
                      self.camera_device, self.gnss_channel, self.gnss_baudrate]:
            widget.setStyleSheet(input_style)
        
        # SSH配置行
        form_layout.addRow("目标设备IP：", self.target_ip)
        form_layout.addRow("SSH用户名：", self.username)
        form_layout.addRow("SSH密码：", self.password)
        
        # VIN码
        form_layout.addRow("车辆VIN码：", self.vehicle_vin)
        
        # 中间激光雷达配置行
        mid_lidar_widget = QWidget()
        mid_lidar_layout = QHBoxLayout(mid_lidar_widget)
        mid_lidar_layout.addWidget(QLabel("中间32线激光雷达IP："))
        mid_lidar_layout.addWidget(self.mid_lidar_ip)
        mid_lidar_layout.addWidget(QLabel("数据端口："))
        mid_lidar_layout.addWidget(self.mid_lidar_port)
        form_layout.addRow("", mid_lidar_widget)
        
        # 左右激光雷达配置行
        side_lidar_widget = QWidget()
        side_lidar_layout = QHBoxLayout(side_lidar_widget)
        side_lidar_layout.addWidget(QLabel("左16线激光雷达端口："))
        side_lidar_layout.addWidget(self.left_lidar_port)
        side_lidar_layout.addWidget(QLabel("右16线激光雷达端口："))
        side_lidar_layout.addWidget(self.right_lidar_port)
        form_layout.addRow("", side_lidar_widget)
        
        # CAN通道配置行
        can_widget = QWidget()
        can_layout = QHBoxLayout(can_widget)
        can_layout.addWidget(QLabel("毫米波雷达通道："))
        can_layout.addWidget(self.mmwave_channel)
        can_layout.addWidget(QLabel("底盘通讯通道："))
        can_layout.addWidget(self.chassis_channel)
        form_layout.addRow("", can_widget)
        
        # 摄像头配置行
        camera_widget = QWidget()
        camera_layout = QHBoxLayout(camera_widget)
        camera_layout.addWidget(QLabel("摄像头设备："))
        camera_layout.addWidget(self.camera_device)
        form_layout.addRow("", camera_widget)
        
        # 组合惯导配置行
        gnss_widget = QWidget()
        gnss_layout = QHBoxLayout(gnss_widget)
        gnss_layout.addWidget(QLabel("组合惯导通道号："))
        gnss_layout.addWidget(self.gnss_channel)
        gnss_layout.addWidget(QLabel("波特率："))
        gnss_layout.addWidget(self.gnss_baudrate)
        form_layout.addRow("", gnss_widget)
        
        layout.addWidget(form_frame)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        deploy_btn = QPushButton("点击部署")
        deploy_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 30px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        deploy_btn.clicked.connect(self.deploy)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 30px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(deploy_btn)
        button_layout.addWidget(cancel_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
    def deploy(self):
        """生成并部署配置文件"""
        # 检查必填字段
        if not self.target_ip.text() or not self.username.text() or not self.password.text():
            QMessageBox.warning(self, "错误", "请填写目标设备IP、SSH用户名和密码！")
            return
            
        try:
            # 生成脚本内容
            script_content = self.generate_script_content()
            
            # 创建临时文件
            local_path = 'RS232.py'
            with open(local_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
                
            # 执行SSH部署
            self.deploy_via_ssh(local_path)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"部署失败：{str(e)}")
            return
            
    def deploy_via_ssh(self, local_path):
        """通过SSH部署文件到目标设备"""
        try:
            # 创建SSH客户端
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 连接到目标设备
            ssh.connect(
                self.target_ip.text(),
                username=self.username.text(),
                password=self.password.text()
            )
            
            # 获取用户家目录
            stdin, stdout, stderr = ssh.exec_command('echo $HOME')
            home_dir = stdout.read().decode().strip()
            print(f"部署目标目录: {home_dir}")
            
            # 检查Python3是否可用
            stdin, stdout, stderr = ssh.exec_command('which python3')
            python_path = stdout.read().decode().strip()
            if not python_path:
                raise Exception("目标设备未安装Python3")
            
            # 检查必要的Python包
            required_packages = ['serial']
            for package in required_packages:
                stdin, stdout, stderr = ssh.exec_command(f'{python_path} -c "import {package}"')
                if stderr.read():
                    raise Exception(f"目标设备缺少必要的Python包: {package}")
            
            # 使用多种方法查找并杀死占用5488端口的进程
            print("检查并清理端口占用...")
            # 1. 使用netstat查找
            stdin, stdout, stderr = ssh.exec_command("netstat -tunlp 2>/dev/null | grep ':5488' | awk '{print $7}' | cut -d'/' -f1")
            pids = stdout.read().decode().strip().split('\n')
            
            # 2. 使用lsof查找（备用方法）
            stdin, stdout, stderr = ssh.exec_command("lsof -ti:5488 2>/dev/null")
            pids.extend(stdout.read().decode().strip().split('\n'))
            
            # 3. 查找Python进程
            stdin, stdout, stderr = ssh.exec_command('ps aux | grep "[R]S232.py" | awk \'{print $2}\'')
            pids.extend(stdout.read().decode().strip().split('\n'))
            
            # 清理所有找到的进程
            for pid in pids:
                if pid.strip():  # 确保PID不为空
                    print(f"正在终止进程: {pid}")
                    ssh.exec_command(f'kill -9 {pid}')
            
            # 等待进程完全终止
            time.sleep(2)
            
            # 使用SFTP传输文件
            remote_path = f'{home_dir}/RS232.py'
            print(f"正在传输文件到: {remote_path}")
            sftp = ssh.open_sftp()
            sftp.put(local_path, remote_path)
            sftp.close()
            
            # 设置执行权限
            ssh.exec_command(f'chmod +x {remote_path}')
            
            # 在后台启动脚本，并将输出重定向到日志文件
            log_path = f'{home_dir}/RS232.log'
            cmd = f'nohup {python_path} {remote_path} > {log_path} 2>&1 &'
            ssh.exec_command(cmd)
            
            # 验证进程是否成功启动
            time.sleep(1)  # 等待进程启动
            stdin, stdout, stderr = ssh.exec_command('ps aux | grep "[R]S232.py" | awk \'{print $2}\'')
            new_pid = stdout.read().decode().strip()
            
            if not new_pid:
                # 如果进程未启动，检查日志文件
                stdin, stdout, stderr = ssh.exec_command(f'tail -n 5 {log_path}')
                log_content = stdout.read().decode().strip()
                raise Exception(f"进程启动失败，日志内容：\n{log_content}")
            
            # 关闭SSH连接
            ssh.close()
            
            QMessageBox.information(
                self, 
                "成功", 
                f"部署完成！\n"
                f"脚本路径: {remote_path}\n"
                f"日志文件: {log_path}\n"
                f"进程 PID: {new_pid}"
            )
            self.accept()
            
        except Exception as e:
            raise Exception(f"部署失败: {str(e)}")
            
    def generate_script_content(self):
        """生成脚本内容"""
        script = '''import os
import sys

# 添加本地库路径到 Python 路径
script_dir = os.path.dirname(os.path.abspath(__file__))
lib_path = os.path.join(script_dir, 'lib', 'python', 'site-packages')
lib_path = os.path.abspath(lib_path)
if lib_path not in sys.path:
    sys.path.insert(0, lib_path)

import time
import subprocess
import socket
import threading
import serial
import contextlib
import queue
import signal
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(os.path.dirname(__file__), "RS232.log"))
    ]
)
logger = logging.getLogger(__name__)

# 配置参数
SERVER_IP = '*************' # 监控服务器IP
SERVER_PORT = 5488          # 监控服务器端口
SERIAL_PORT = '/dev/ttyUART_232_C'
LIDAR_IPS = {
    'mid_32': ('*************', 2368),   # 中间32线激光雷达
    'left_16': ('*************', 2366),  # 左边16线激光雷达
    'right_16': ('*************', 2370)  # 右边16线激光雷达
}
CAN_INTERFACES = ['can0', 'can1']
CAMERA_DEVICE = '/dev/video0'  # 摄像头设备
CAMERA_TEST_FILE = '/tmp/camera_test.jpg'  # 测试文件路径

# 全局状态变量和线程安全队列
status = {
    'serial_active': 0,    # 组合惯导
    'mid_32_active': 0,    # 中间32线激光雷达
    'left_16_active': 0,   # 左边16线激光雷达
    'right_16_active': 0,  # 右边16线激光雷达
    'can0_active': 0,      # CAN0总线
    'can1_active': 0,      # CAN1总线
    'camera_active': 0,    # 摄像头状态
    'radio_active': 1,     # 电台设备（始终正常）
    'ultrasonic_active': 1, # 超声波雷达（始终正常）
    'vin': 'LS6A2E167PA503004'  # VIN码
}

# 线程安全的状态锁
status_lock = threading.Lock()

# 用于优雅退出的事件
shutdown_event = threading.Event()

def update_status(key, value):
    """线程安全的状态更新"""
    with status_lock:
        status[key] = value

def check_serial():
    """检测组合惯导数据流（协议验证 + 时间窗口）"""
    while not shutdown_event.is_set():
        try:
            with contextlib.closing(serial.Serial(SERIAL_PORT, 230400, timeout=1)) as ser:
                logger.info(f"串口监听已启动: {SERIAL_PORT}")
                buffer = ''
                last_valid_time = 0
                while not shutdown_event.is_set():
                    try:
                        ser.reset_input_buffer()
                        data = ser.read(ser.in_waiting or 1).decode('ascii', errors='ignore')
                        if data:
                            buffer += data
                            if '$GPCHC' in buffer:  # 检测有效协议头
                                last_valid_time = time.time()
                                update_status('serial_active', 1)
                                buffer = ''
                        else:
                            if time.time() - last_valid_time > 5:
                                update_status('serial_active', 0)
                        time.sleep(0.1)
                    except serial.SerialException:
                        break
        except Exception as e:
            logger.error(f"串口检测异常: {e}")
            update_status('serial_active', 0)
        time.sleep(1)  # 重连延迟

def check_lidar(lidar_name, ip_port):
    """检测指定激光雷达的UDP数据流"""
    ip, port = ip_port
    while not shutdown_event.is_set():
        try:
            with contextlib.closing(socket.socket(socket.AF_INET, socket.SOCK_DGRAM)) as sock:
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                sock.bind((ip, port))
                sock.setblocking(False)
                logger.info(f"激光雷达监听已启动: {lidar_name} - {ip}:{port}")

                while not shutdown_event.is_set():
                    try:
                        data, _ = sock.recvfrom(4096)
                        update_status(f'{lidar_name}_active', 1 if data else 0)
                    except BlockingIOError:
                        update_status(f'{lidar_name}_active', 0)
                    except Exception:
                        break
                    time.sleep(1)
        except Exception as e:
            logger.error(f"激光雷达检测异常 [{lidar_name}]: {e}")
            update_status(f'{lidar_name}_active', 0)
        time.sleep(1)  # 重连延迟

def check_can(interface):
    """检测CAN总线活动"""
    while not shutdown_event.is_set():
        try:
            proc = subprocess.Popen(
                ['timeout', '1', 'candump', interface],
                stdout=subprocess.PIPE,
                stderr=subprocess.DEVNULL
            )
            output, _ = proc.communicate()
            active = 1 if output else 0
            update_status(f"can0_active" if interface == "can0" else "can1_active", active)
        except:
            update_status(f"can0_active" if interface == "can0" else "can1_active", 0)
        time.sleep(2)

def check_camera():
    """检测摄像头设备状态"""
    # 构建ffmpeg命令
    cmd = [
        'ffmpeg',
        '-f', 'v4l2',
        '-input_format', 'nv16',
        '-framerate', '30',
        '-video_size', '1280x720',
        '-i', CAMERA_DEVICE,
        '-vframes', '1',
        CAMERA_TEST_FILE
    ]

    while not shutdown_event.is_set():
        try:
            # 运行ffmpeg命令捕获图像
            result = subprocess.run(
                cmd,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                timeout=5
            )

            # 检查文件是否存在
            if os.path.exists(CAMERA_TEST_FILE):
                update_status('camera_active', 1)
                try:
                    os.remove(CAMERA_TEST_FILE)  # 删除测试文件
                except:
                    pass
            else:
                update_status('camera_active', 0)
        except subprocess.TimeoutExpired:
            logger.warning("摄像头检测超时")
            update_status('camera_active', 0)
        except Exception as e:
            logger.error(f"摄像头检测异常: {e}")
            update_status('camera_active', 0)

        # 每10秒检测一次
        time.sleep(10)

def send_status_to_server():
    """连接到监控服务器并持续发送状态数据"""
    while not shutdown_event.is_set():
        try:
            logger.info(f"正在尝试连接到监控服务器 {SERVER_IP}:{SERVER_PORT}...")
            with contextlib.closing(socket.socket(socket.AF_INET, socket.SOCK_STREAM)) as sock:
                sock.connect((SERVER_IP, SERVER_PORT))
                logger.info("成功连接到监控服务器。")

                while not shutdown_event.is_set():
                    try:
                        with status_lock:
                            # 按新顺序构建状态字符串
                            data = (
                                f"{status['serial_active']},"     # 组合惯导
                                f"{status['mid_32_active']},"     # 中间32线激光雷达
                                f"{status['left_16_active']},"    # 左边16线激光雷达
                                f"{status['right_16_active']},"   # 右边16线激光雷达
                                f"{status['can0_active']},"       # CAN0总线
                                f"{status['can1_active']},"       # CAN1总线
                                f"{status['camera_active']},"     # 摄像头
                                f"{status['radio_active']},"      # 电台
                                f"{status['ultrasonic_active']}," # 超声波雷达
                                f"{status['vin']}"               # VIN码
                            )
                        sock.sendall(data.encode() + b'\\n')
                        print(f"发送状态数据: {data}")
                        time.sleep(1) # 每秒发送一次
                    except (socket.error, BrokenPipeError):
                        logger.warning("与服务器的连接丢失，正在尝试重新连接...")
                        break # Break inner loop to reconnect
        except (socket.error, ConnectionRefusedError) as e:
            logger.error(f"无法连接到监控服务器: {e}")

        if not shutdown_event.is_set():
            time.sleep(5) # Reconnection delay

def signal_handler(signum, frame):
    """处理退出信号"""
    logger.info("正在关闭服务...")
    shutdown_event.set()

if __name__ == '__main__':
    try:
        # 记录启动信息
        logger.info("服务启动中...")
        logger.info(f"工作目录: {os.getcwd()}")
        logger.info(f"Python版本: {sys.version}")

        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # 启动所有监测线程
        threads = [
            threading.Thread(target=check_serial, daemon=True),
            # 启动三个激光雷达检测线程
            threading.Thread(target=check_lidar, args=('mid_32', LIDAR_IPS['mid_32']), daemon=True),
            threading.Thread(target=check_lidar, args=('left_16', LIDAR_IPS['left_16']), daemon=True),
            threading.Thread(target=check_lidar, args=('right_16', LIDAR_IPS['right_16']), daemon=True),
            # CAN总线检测
            threading.Thread(target=check_can, args=('can0',), daemon=True),
            threading.Thread(target=check_can, args=('can1',), daemon=True),
            # 摄像头检测
            threading.Thread(target=check_camera, daemon=True),
            # TCP客户端，用于发送数据
            threading.Thread(target=send_status_to_server, daemon=True)
        ]

        for thread in threads:
            thread.start()

        # 主线程等待退出信号
        while not shutdown_event.is_set():
            time.sleep(0.1)

    except KeyboardInterrupt:
        logger.info("接收到退出信号...")
    except Exception as e:
        logger.error(f"服务异常: {e}")
    finally:
        shutdown_event.set()
        # 清理临时文件
        if os.path.exists(CAMERA_TEST_FILE):
            try:
                os.remove(CAMERA_TEST_FILE)
            except:
                pass
        # 等待所有线程退出
        for thread in threads:
            thread.join(timeout=2)
        logger.info("服务已关闭")
'''
        return script