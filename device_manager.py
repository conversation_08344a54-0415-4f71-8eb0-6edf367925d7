from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                           QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                           QHeaderView, QMessageBox, QFrame)
from PyQt5.QtCore import Qt, pyqtSignal
import json
import os

class DeviceManagerDialog(QDialog):
    # Signal to indicate that the device list has been updated
    devices_updated = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("设备管理")
        self.setMinimumWidth(600)
        self.setMinimumHeight(400)
        self.setStyleSheet("background-color: white;")
        self.devices = self.load_devices()
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # 标题
        title = QLabel("设备管理")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px 0;
            }
        """)
        layout.addWidget(title, alignment=Qt.AlignCenter)
        
        # 添加设备区域
        add_device_frame = QFrame()
        add_device_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #e0e0e0;
                padding: 15px;
            }
        """)
        add_device_layout = QHBoxLayout(add_device_frame)
        
        # VIN输入框
        self.vin_input = QLineEdit()
        self.vin_input.setPlaceholderText("请输入车辆VIN码")
        self.vin_input.setMinimumWidth(200)
        
        # 添加按钮
        add_btn = QPushButton("绑定设备")
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        add_btn.clicked.connect(self.add_device)
        
        add_device_layout.addWidget(QLabel("VIN码："))
        add_device_layout.addWidget(self.vin_input)
        add_device_layout.addWidget(add_btn)
        
        layout.addWidget(add_device_frame)
        
        # 设备列表
        self.device_table = QTableWidget()
        self.device_table.setColumnCount(2)
        self.device_table.setHorizontalHeaderLabels(["VIN码", "操作"])
        self.device_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.device_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Fixed)
        self.device_table.setColumnWidth(1, 100)
        
        self.refresh_device_table()
        layout.addWidget(self.device_table)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        close_btn = QPushButton("关闭")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 8px 20px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        close_btn.clicked.connect(self.accept)
        
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        layout.addLayout(button_layout)
        
    def add_device(self):
        vin = self.vin_input.text().strip()

        if not vin:
            QMessageBox.warning(self, "错误", "VIN码不能为空！")
            return

        # 检查是否已存在
        for device in self.devices:
            if device["vin"] == vin:
                QMessageBox.warning(self, "错误", "该VIN码已存在！")
                return

        # 添加新设备
        self.devices.append({
            "vin": vin
        })

        # 保存并刷新
        self.save_devices()
        self.refresh_device_table()

        # 清空输入框
        self.vin_input.clear()
        
    def delete_device(self, vin):
        self.devices = [d for d in self.devices if d["vin"] != vin]
        self.save_devices()
        self.refresh_device_table()
        
    def refresh_device_table(self):
        self.device_table.setRowCount(0)
        for device in self.devices:
            row = self.device_table.rowCount()
            self.device_table.insertRow(row)

            # 添加设备信息
            self.device_table.setItem(row, 0, QTableWidgetItem(device["vin"]))

            # 添加删除按钮
            delete_btn = QPushButton("删除")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            delete_btn.clicked.connect(lambda checked, vin=device["vin"]: self.delete_device(vin))
            self.device_table.setCellWidget(row, 1, delete_btn)
            
    def load_devices(self):
        try:
            if os.path.exists("devices.json"):
                with open("devices.json", "r", encoding="utf-8") as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载设备配置失败: {e}")
        return []
        
    def save_devices(self):
        """Saves the current list of devices back to the JSON file."""
        try:
            with open("devices.json", "w", encoding='utf-8') as f:
                json.dump(self.devices, f, indent=4)
            # Emit the signal after successfully saving
            self.devices_updated.emit()
            QMessageBox.information(self, "成功", "设备列表已成功保存！")
            self.accept() # Close the dialog
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存设备失败: {str(e)}")

    def get_devices(self):
        return self.devices 

    def closeEvent(self, event):
        # This can be used to emit the signal as well, ensuring updates even on close
        # self.devices_updated.emit()
        super().closeEvent(event) 