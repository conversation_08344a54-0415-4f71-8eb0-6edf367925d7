import os
import json
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QFrame, QPushButton, QGridLayout, QSpacerItem, 
                            QSizePolicy, QScrollArea)
from PyQt5.QtCore import Qt, QSize, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QIcon, QFont, QPixmap, QColor, QPainter, QBrush, QPen, QPalette, QLinearGradient
from chat_dialog import ChatDialog # 导入聊天对话框

class QuickAccessButton(QPushButton):
    """首页上的快速访问按钮，带图标和文字"""
    def __init__(self, title, icon_path, description="", parent=None):
        super().__init__(parent)
        self.title = title
        self.description = description
        self.setFixedSize(180, 160)  # Slightly larger button size
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: rgba(255, 255, 255, 1);
                color: black;
                border: none;
                border-radius: 16px;
                padding: 12px;
                font-size: 16px;  /* 设置首页快速访问按钮的字体大小 */
                font-weight: bold;  /* 设置首页快速访问按钮的字体粗细 */
                font-family: SimSun;  /* 设置首页快速访问按钮的字体系列 */
                text-align: center;
            }}
            QPushButton:hover {{
                background-color: rgba(255, 255, 255, 1);
                border: none;
            }}
            QPushButton:pressed {{
                background-color: rgba(255, 255, 255, 1);
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 15, 10, 15)  # Adjusted margins for content
        layout.setSpacing(12)  # Increased spacing between icon and text
        layout.setAlignment(Qt.AlignCenter)
        
        icon_label = QLabel()
        pixmap = QPixmap(icon_path)
        if not pixmap.isNull():
            icon_label.setPixmap(pixmap.scaled(72, 72, Qt.KeepAspectRatio, Qt.SmoothTransformation)) # Larger icon
        else:
            placeholder = QPixmap(72, 72)
            placeholder.fill(Qt.lightGray)
            icon_label.setPixmap(placeholder)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("background-color: transparent;")
        
        text_label = QLabel(title)
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setStyleSheet(f"""
            font-size: 16px;
            font-weight: bold;
            color: black;
            background-color: transparent;
            font-family: SimSun;
        """)
        
        layout.addWidget(icon_label)
        layout.addWidget(text_label)
        
        # Clear original text and icon if any were set by base class (though we avoided it)
        self.setText("")
        self.setIcon(QIcon())

class StatisticsCard(QFrame):
    """显示统计数据的卡片"""
    def __init__(self, title, value, icon_path=None, color="#1C64F2", parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.adaptive_manager = None  # 将在主窗口中设置

        # 设置自适应尺寸
        self.update_adaptive_size()
        
        # 调整颜色使其在蓝色背景上更突出
        darker_color = self.darken_color(color)
        
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {darker_color};
                color:                       ;
                border-radius: 64px; /* 增加卡片的圆角半径，使卡片四角更圆滑 */
                padding: 0px; /* 调整卡片内部的内边距 */
                border: none; /* 移除边框 */
            }}
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15) # Adjusted margins
        layout.setSpacing(8)  # Adjusted spacing for content
        layout.setAlignment(Qt.AlignCenter)

        layout.addStretch(1) # Push content to center vertically

        if icon_path:
            icon_label = QLabel()
            icon_label.setFixedSize(72, 72)  # Larger icon size
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setStyleSheet("background-color: transparent;")

            pixmap = QPixmap(icon_path)
            if not pixmap.isNull():
                scaled_pixmap = pixmap.scaled(72, 72, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon_label.setPixmap(scaled_pixmap)
            else:
                placeholder_pixmap = QPixmap(72, 72)
                placeholder_pixmap.fill(Qt.lightGray)
                icon_label.setPixmap(placeholder_pixmap)
            
            layout.addWidget(icon_label, 0, Qt.AlignCenter) # Icon at the top

        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"""
            font-size: 28px;
            font-weight: bold;
            color:                       ;
            margin: 5px 0px;
            padding: 0px;
            background-color: transparent;
            font-family: SimSun;
        """)
        layout.addWidget(value_label, 0, Qt.AlignCenter)

        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            font-size: 16px;
            color: rgba(0,0,0, 0.9);
            margin: 0px;
            padding: 0px;
            background-color: transparent;
            font-family: SimSun;
        """)
        title_label.setWordWrap(True)
        layout.addWidget(title_label, 0, Qt.AlignCenter)

        layout.addStretch(1) # Push content to center vertically
    
    def darken_color(self, color):
        """将颜色调暗，使其在蓝色背景上更突出"""
        if color.startswith('#'):
            # 将十六进制转换为RGB
            r = int(color[1:3], 16)
            g = int(color[3:5], 16)
            b = int(color[5:7], 16)
            
            # 调整颜色值，使其更深
            r = max(0, r - 40)
            g = max(0, g - 40)
            b = max(0, b - 40)
            
            # 转回十六进制
            return f"#{r:02x}{g:02x}{b:02x}"
        return color
    
    def adjust_color(self, color):
        """调整颜色，创建渐变效果"""
        # 简单的颜色调整，使渐变更明显
        if color.startswith('#'):
            # 将十六进制转换为RGB
            r = int(color[1:3], 16)
            g = int(color[3:5], 16)
            b = int(color[5:7], 16)
            
            # 调整颜色值，使其更深
            r = max(0, r - 30)
            g = max(0, g - 30)
            b = max(0, b - 30)
            
            # 转回十六进制
            return f"#{r:02x}{g:02x}{b:02x}"
        return color

    def update_adaptive_size(self):
        """根据窗口状态更新卡片尺寸"""
        # 获取父窗口状态
        is_maximized = False
        if (self.parent_window and
            hasattr(self.parent_window, 'is_maximized')):
            is_maximized = self.parent_window.is_maximized
        elif (self.adaptive_manager and
              self.adaptive_manager.current_state == "maximized"):
            is_maximized = True

        # 根据窗口状态设置尺寸
        if is_maximized:
            # 最大化时使用较大尺寸
            self.setFixedSize(720, 210)
        else:
            # 恢复窗口时使用较小尺寸
            self.setFixedSize(600, 170)

class HomePageWidget(QWidget):
    """首页界面，显示智测魔方概览和快速入口"""
    # 定义导航信号
    navigate_to_category = pyqtSignal(str)
    launch_app = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.apps_data = self.load_config()
        self.init_ui()
        
    def load_config(self):
        """加载应用配置数据"""
        config_file = "apps_config.json"
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return {}
        return {}
        
    def find_app_by_name(self, name):
        """根据名称在apps_data中查找应用"""
        for category_content in self.apps_data.values():
            if isinstance(category_content, list):
                for app in category_content:
                    if app.get("name") == name:
                        return app
            elif isinstance(category_content, dict):
                for sub_category_content in category_content.values():
                    if isinstance(sub_category_content, list):
                        for app in sub_category_content:
                            if app.get("name") == name:
                                return app
        return None

    def open_chat_dialog(self):
        """打开聊天对话框"""
        # 确保同一时间只有一个对话框实例
        if not hasattr(self, 'chat_dialog') or not self.chat_dialog.isVisible():
            self.chat_dialog = ChatDialog(self.parent_window)
            # 将对话框移动到主窗口的右下角
            if self.parent_window:
                main_win_geom = self.parent_window.geometry()
                dialog_geom = self.chat_dialog.geometry()
                x = main_win_geom.right() - dialog_geom.width() - 20
                y = main_win_geom.bottom() - dialog_geom.height() - 80 # A bit of offset from bottom
                self.chat_dialog.move(x, y)
            self.chat_dialog.show()
        
    def init_ui(self):
        self.setStyleSheet(f"QWidget {{ background-color: #3a7bd9; }}")
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(35, 35, 35, 35)  # Increased margins
        main_layout.setSpacing(35)  # Increased spacing
        
        # 欢迎标题部分 - 使用图片背景
        welcome_frame = QFrame()
        welcome_frame.setFixedHeight(300)  # 设置固定高度，例如300像素

        # Ensure the path uses forward slashes for cross-platform compatibility in stylesheets
        image_path = "../image/home_bg.png".replace("\\", "/")
        welcome_frame.setStyleSheet(f"""
            QFrame {{
                border-image: url({image_path}) 0 0 0 0 stretch stretch;
                border-radius: 20px; 
                padding: 35px; 
            }}
        """)
        welcome_layout = QVBoxLayout(welcome_frame)
        welcome_layout.setContentsMargins(25, 0, 25, 0)  # Increased internal padding
        welcome_layout.setAlignment(Qt.AlignCenter)
        
        # 主标题 - 居中显示，增大字体，彻底去掉小背景
        welcome_title = QLabel("智测魔方")
        welcome_title.setAlignment(Qt.AlignCenter)
        welcome_title.setAutoFillBackground(False)  # 禁用自动填充背景
        welcome_title.setAttribute(Qt.WA_TranslucentBackground, True)  # 设置透明背景属性

        # 设置调色板确保背景透明
        palette = welcome_title.palette()
        palette.setColor(QPalette.Background, QColor(0, 0, 0, 0))  # 完全透明
        welcome_title.setPalette(palette)

        welcome_title.setStyleSheet(f"""
            font-size: 96px;
            font-weight: bold;
            color: white;
            background: transparent !important;
            background-color: transparent !important;
            background-image: none !important;
            font-family: SimSun;
            border: none !important;
            border-radius: 0px !important;
            padding: 0px !important;
            margin: 0px !important;
        """)
        welcome_layout.addWidget(welcome_title)
        welcome_layout.addSpacing(25)  # 添加30像素的间隔，可以调整数值
        # 副标题 - 居中显示，调整字体，彻底去掉小背景
        welcome_subtitle = QLabel("欢迎来到您的智能网联汽车装调与测试的解决方案")
        welcome_subtitle.setAlignment(Qt.AlignCenter)
        welcome_subtitle.setAutoFillBackground(False)  # 禁用自动填充背景
        welcome_subtitle.setAttribute(Qt.WA_TranslucentBackground, True)  # 设置透明背景属性

        # 设置调色板确保背景透明
        palette_subtitle = welcome_subtitle.palette()
        palette_subtitle.setColor(QPalette.Background, QColor(0, 0, 0, 0))  # 完全透明
        welcome_subtitle.setPalette(palette_subtitle)

        welcome_subtitle.setStyleSheet(f"""
            font-size: 36px;
            color: rgba(255, 255, 255, 0.95);
            background: transparent !important;
            background-color: transparent !important;
            background-image: none !important;
            font-family: SimSun;
            border: none !important;
            border-radius: 0px !important;
            padding: 0px !important;
            margin: 30px !important;
        """)
        welcome_layout.addWidget(welcome_subtitle)
        
        main_layout.addWidget(welcome_frame)
        
        # 统计卡片区域
        stats_container = QFrame()
        stats_container.setObjectName("statsContainer")
        stats_container.setStyleSheet("""
            QFrame#statsContainer {
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 20px;
                margin: 5px;
            }
        """)
        stats_layout = QGridLayout(stats_container)
        stats_layout.setContentsMargins(25, 25, 25, 25)  # Increased padding for border
        stats_layout.setSpacing(50)  # Adjusted spacing between cards from 35 to 30
        
        # 添加统计卡片
        stats_cards = [
            ("在线", "状态监控系统", "../image/connect.png", "rgba(201, 240, 255, 1)"),
            ("在线", "设备状态", "../image/connect.png", "rgba(201, 240, 255, 1)"),
        ]
        
        # 每行显示4个卡片，确保居中对齐
        for i, (title, value, icon, color) in enumerate(stats_cards):
            card = StatisticsCard(title, value, icon, color, self.parent_window)
            # 设置自适应管理器
            if (self.parent_window and
                hasattr(self.parent_window, 'adaptive_manager')):
                card.adaptive_manager = self.parent_window.adaptive_manager
                card.update_adaptive_size()
            col = i % 4 + 1
            stats_layout.addWidget(card, 0, col, Qt.AlignCenter)
        
        # 设置列伸缩因子，确保卡片均匀分布
        for i in range(4):
            stats_layout.setColumnStretch(i, 1)
            
        main_layout.addWidget(stats_container)
        
        # 快速访问区域
        section_title = QLabel("快速访问")
        section_title.setStyleSheet(f"""
            font-size: 26px;
            font-weight: bold;
            color: black;
            margin-top: 20px;
            background-color: transparent;
            font-family: SimSun;
        """)
        main_layout.addWidget(section_title)
        
        quick_access_container = QFrame()
        quick_access_container.setObjectName("quickAccessContainer")
        quick_access_container.setStyleSheet("""
            QFrame#quickAccessContainer {
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 20px;
                margin: 5px;
            }
        """)
        # 使用网格布局代替水平布局
        quick_access_layout = QGridLayout(quick_access_container)
        quick_access_layout.setContentsMargins(25, 25, 25, 25)  # Increased padding for border
        quick_access_layout.setSpacing(30)  # Increased spacing between buttons
        
        # 定义快速访问按钮及其操作
        buttons_config = [
            {"name": "工具箱自检", "icon": "../image/examine.png", "description": "工具箱自检工具", "action_type": "launch_app", "action_param": "工具箱自检"},            
            {"name": "毫米波雷达数据解析", "icon": "../image/radar.png", "description": "毫米波雷达原始数据解析器", "action_type": "launch_app", "action_param": "毫米波雷达原始数据解析器"},
            {"name": "项目进度监控", "icon": "../image/监控.png", "description": "项目进度监控", "action_type": "launch_app", "action_param": "项目进度监控"},
            {"name": "帮助助手", "icon": "../image/chatgpt.png", "description": "智能帮助助手", "action_type": "chat", "action_param": None},
            {"name": "大陆雷达", "icon": "../image/Radar_Monitor.png", "description": "大陆雷达调试工具", "action_type": "launch_app", "action_param": "大陆雷达调试工具"},
            {"name": "激光雷达", "icon": "../image/leishen.png", "description": "激光雷达上位机", "action_type": "launch_app", "action_param": "激光雷达上位机"},
            {"name": "wireshark", "icon": "../image/wireshark.png", "description": "wireshark", "action_type": "launch_app", "action_param": "wireshark网络抓包工具"},
            {"name": "USB_CAN_Tool", "icon": "../image/USB_CAN_Tool.png", "description": "USB_CAN_Tool", "action_type": "launch_app", "action_param": "USB_CAN_Tool"}
        ]
        
        # 每行显示4个按钮
        max_cols = 4
        for i, config in enumerate(buttons_config):
            btn = QuickAccessButton(config["name"], config["icon"], config["description"])
            
            action_type = config["action_type"]
            action_param = config["action_param"]

            if action_type == "navigate":
                btn.clicked.connect(lambda _, p=action_param: self.navigate_to_category.emit(p))
            elif action_type == "launch_app":
                app_data = self.find_app_by_name(action_param)
                if app_data:
                    btn.clicked.connect(lambda _, app=app_data: self.launch_app.emit(app))
                else:
                    # 如果找不到应用，可以设置为禁用或导航到相关分类
                    btn.clicked.connect(lambda _, p=action_param: self.navigate_to_category.emit(p))
            elif action_type == "chat":
                btn.clicked.connect(self.open_chat_dialog)

            row = i // max_cols
            col = i % max_cols
            quick_access_layout.addWidget(btn, row, col)
        
        main_layout.addWidget(quick_access_container)
