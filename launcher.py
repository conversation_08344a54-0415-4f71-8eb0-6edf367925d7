#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windows程序启动器 (无窗口版本)
用于启动指定的Python脚本
"""

import subprocess
import sys
import os
from pathlib import Path
import ctypes

# 配置路径
PYTHON_EXE = r"D:\Programs\Python\Python313\python.exe"
MAIN_SCRIPT = r"D:\Programs\tool\main\main.py"

# Windows API常量
MB_OK = 0x0
MB_ICONERROR = 0x10
MB_ICONINFORMATION = 0x40

def show_message_box(title, message, icon=MB_ICONINFORMATION):
    """显示Windows消息框而不是使用print"""
    ctypes.windll.user32.MessageBoxW(0, message, title, MB_OK | icon)

def check_paths():
    """检查路径是否存在"""
    python_path = Path(PYTHON_EXE)
    script_path = Path(MAIN_SCRIPT)

    if not python_path.exists():
        show_message_box("错误", f"Python解释器路径不存在:\n{PYTHON_EXE}", MB_ICONERROR)
        return False

    if not script_path.exists():
        show_message_box("错误", f"主脚本路径不存在:\n{MAIN_SCRIPT}", MB_ICONERROR)
        return False

    return True

def launch_program():
    """启动主程序"""
    try:
        # 构建命令
        command = [PYTHON_EXE, MAIN_SCRIPT]

        # 启动程序 - 使用Popen而不是run，这样不会等待程序结束
        # 使用DETACHED_PROCESS和CREATE_NO_WINDOW标志使子进程与父进程分离且不显示窗口
        DETACHED_PROCESS = 0x00000008
        CREATE_NO_WINDOW = 0x08000000
        subprocess.Popen(
            command,
            creationflags=DETACHED_PROCESS | CREATE_NO_WINDOW,
            cwd=os.path.dirname(MAIN_SCRIPT),  # 设置工作目录为脚本所在目录
            shell=False
        )

        return 0

    except FileNotFoundError as e:
        show_message_box("错误", f"找不到文件:\n{str(e)}", MB_ICONERROR)
        return 1
    except Exception as e:
        show_message_box("错误", f"启动程序时发生错误:\n{str(e)}", MB_ICONERROR)
        return 1

def main():
    """主函数 - 无控制台输出"""
    # 检查路径
    if not check_paths():
        return 1

    # 启动程序
    return launch_program()

if __name__ == "__main__":
    sys.exit(main())
