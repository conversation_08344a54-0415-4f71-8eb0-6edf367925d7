import os
import sys
import time
from datetime import datetime
import shutil
import re
from collections import defaultdict
import atexit

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QTableWidget, QTableWidgetItem, QHeaderView, QFileDialog,
                            QMessageBox, QComboBox, QCheckBox, QGroupBox, QLineEdit,
                            QDateEdit, QSplitter, QTextEdit, QTabWidget, QFrame, QListWidget,
                            QListWidgetItem, QAbstractItemView, QStackedWidget, QButtonGroup)
from PyQt5.QtCore import Qt, QSize, pyqtSignal, QDate, QThread, pyqtSlot, QTimer
from PyQt5.QtGui import QIcon, QFont, QColor

from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from matplotlib import pyplot as plt

# 颜色配置已内联到代码中

# --- Logger Class ---
class ToolboxLogger:
    _instance = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(ToolboxLogger, cls).__new__(cls)
        return cls._instance

    def __init__(self, log_dir="logs/toolbox"):
        if hasattr(self, '_initialized'):
            return
        self.log_dir = log_dir
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
        
        today = datetime.now().strftime("%Y-%m-%d")
        self.log_file = os.path.join(self.log_dir, f"log_{today}.log")
        self._initialized = True

    def _write_log(self, message):
        """Writes a message to the log file with a timestamp."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(f"[{timestamp}] {message}\n")

    def log_event(self, event_type, details):
        """Logs a structured event."""
        self._write_log(f"{event_type}: {details}")
        
    def log_app_start(self):
        self._write_log("工具箱启动")

    def log_app_close(self):
        self._write_log("工具箱关闭")
        
    def log_user_action(self, action_details):
        self._write_log(f"用户操作: {action_details}")

    def log_feature_start(self, feature_name):
        """Logs the start of a feature usage."""
        self._write_log(f"功能开始: {feature_name}")

    def log_feature_end(self, feature_name):
        """Logs the end of a feature usage."""
        self._write_log(f"功能结束: {feature_name}")

# --- Global Logger Instance ---
# Use a function to ensure it's initialized once and can be imported
def get_logger():
    return ToolboxLogger()

# Register the close event at exit
atexit.register(lambda: get_logger().log_app_close())


# Configure Matplotlib to display Chinese characters correctly
plt.rcParams['font.sans-serif'] = ['SimHei']  # Use SimHei font for Chinese characters
plt.rcParams['axes.unicode_minus'] = False  # Display the minus sign correctly

# --- From log_statistics.py ---

class MplCanvas(FigureCanvas):
    """Matplotlib canvas widget."""
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        fig = Figure(figsize=(width, height), dpi=dpi)
        self.axes = fig.add_subplot(111)
        super(MplCanvas, self).__init__(fig)
        self.setParent(parent)

class LogStatisticsWidget(QWidget):
    """Widget to display log statistics with charts."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.log_dir = "logs/toolbox"
        self.init_ui()
        self.update_statistics()

    def init_ui(self):
        """Initializes the UI components."""
        self.setStyleSheet(f"background-color: #ffffff; color: #2c3e50;")

        layout = QVBoxLayout(self)
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet(f"""
            QTabWidget::pane {{
                border-top: 2px solid #dcdcdc;
            }}
            QTabBar::tab {{
                background: #f5f5f5;
                color: #333333;
                padding: 10px;
                border: 1px solid #dcdcdc;
            }}
            QTabBar::tab:selected {{
                background: #ffffff;
                color: #1C64F2;
            }}
        """)

        self.usage_frequency_tab = QWidget()
        self.usage_duration_tab = QWidget()
        self.feature_duration_tab = QWidget()

        self.tabs.addTab(self.usage_frequency_tab, "功能使用频率")
        self.tabs.addTab(self.usage_duration_tab, "工具箱使用时长")
        self.tabs.addTab(self.feature_duration_tab, "单个功能使用时长")

        layout.addWidget(self.tabs)
        self.setLayout(layout)

        # Setup tabs
        self._setup_frequency_tab()
        self._setup_duration_tab()
        self._setup_feature_duration_tab()

    def _create_stat_card(self, title, value):
        """Creates a styled frame for displaying a single statistic."""
        card = QFrame()
        card.setFrameShape(QFrame.StyledPanel)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px;
            }}
        """)
        card_layout = QVBoxLayout(card)

        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"color: #6c757d; font-size: 14px;")

        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"color: #2c3e50; font-size: 24px; font-weight: bold;")

        card_layout.addWidget(value_label)
        card_layout.addWidget(title_label)
        return card

    def _setup_frequency_tab(self):
        """Sets up the feature usage frequency tab."""
        layout = QVBoxLayout(self.usage_frequency_tab)

        self.stats_cards_freq = QHBoxLayout()
        layout.addLayout(self.stats_cards_freq)

        self.frequency_canvas = MplCanvas(self, width=8, height=6, dpi=100)
        layout.addWidget(self.frequency_canvas)

    def _setup_duration_tab(self):
        """Sets up the toolbox usage duration tab."""
        layout = QVBoxLayout(self.usage_duration_tab)

        self.stats_cards_duration = QHBoxLayout()
        layout.addLayout(self.stats_cards_duration)

        self.duration_canvas = MplCanvas(self, width=8, height=6, dpi=100)
        layout.addWidget(self.duration_canvas)

    def _setup_feature_duration_tab(self):
        """Sets up the single feature usage duration tab."""
        layout = QVBoxLayout(self.feature_duration_tab)
        
        # Top bar with filter
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("时间范围:"))
        self.feature_duration_filter = QComboBox()
        self.feature_duration_filter.addItems(["今日", "本月"])
        self.feature_duration_filter.currentIndexChanged.connect(self.update_statistics)
        filter_layout.addWidget(self.feature_duration_filter)
        filter_layout.addStretch()
        layout.addLayout(filter_layout)

        self.feature_duration_canvas = MplCanvas(self, width=8, height=6, dpi=100)
        layout.addWidget(self.feature_duration_canvas)

    def update_statistics(self):
        """Called to refresh all statistics and charts."""
        log_data = self._parse_logs()
        self._plot_usage_frequency(log_data['actions'])
        self._plot_usage_duration(log_data['sessions'])
        self._plot_feature_duration(log_data['feature_durations'])

    def _parse_logs(self):
        """Parses all log files to extract statistical data."""
        action_counts = defaultdict(int)
        session_durations = [] # List of (date_str, duration_minutes)
        feature_durations = [] # List of (feature_name, date_obj, duration_seconds)

        if not os.path.exists(self.log_dir):
            return {'actions': action_counts, 'sessions': session_durations, 'feature_durations': feature_durations}

        for log_file in sorted(os.listdir(self.log_dir)):
            if not log_file.endswith(".log"):
                continue

            file_path = os.path.join(self.log_dir, log_file)
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            session_start_time = None
            feature_start_times = {} # To track start times of features within the log files

            for line in lines:
                # Extract actions
                action_match = re.search(r"用户操作: (.*)", line)
                if action_match:
                    action_detail = action_match.group(1)
                    # For frequency, we can simplify the action, e.g., group all "View FAQ"
                    if "查看FAQ:" in action_detail:
                         action_counts["查看FAQ文档"] += 1
                    elif "FAQ AI查询:" in action_detail:
                         action_counts["AI查询"] += 1
                    else: # General actions
                        action_counts[action_detail] += 1

                # Extract session start time
                start_match = re.search(r"\[(.*?)\] 工具箱启动", line)
                if start_match:
                    try:
                        session_start_time = datetime.strptime(start_match.group(1), '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        session_start_time = None # Ignore malformed timestamp

                # Extract session end time and calculate duration
                end_match = re.search(r"\[(.*?)\] 工具箱关闭", line)
                if end_match and session_start_time:
                    try:
                        end_time = datetime.strptime(end_match.group(1), '%Y-%m-%d %H:%M:%S')
                        duration = (end_time - session_start_time).total_seconds() / 60 # in minutes
                        date_str = session_start_time.strftime('%Y-%m-%d')
                        session_durations.append((date_str, duration))
                    except ValueError:
                        pass # Ignore malformed timestamp
                    finally:
                        session_start_time = None # Reset for next session in the same file

                # --- New parsing logic for feature duration ---
                feature_start_match = re.search(r"\[(.*?)\] 功能开始: (.*)", line)
                if feature_start_match:
                    timestamp_str, feature_name = feature_start_match.groups()
                    feature_name = feature_name.strip()
                    try:
                        feature_start_times[feature_name] = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        continue # Ignore malformed timestamp
                
                feature_end_match = re.search(r"\[(.*?)\] 功能结束: (.*)", line)
                if feature_end_match:
                    timestamp_str, feature_name = feature_end_match.groups()
                    feature_name = feature_name.strip()
                    if feature_name in feature_start_times:
                        try:
                            end_time = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                            start_time = feature_start_times.pop(feature_name) # Remove to avoid reuse
                            duration = (end_time - start_time).total_seconds()
                            if duration >= 0: # Ensure non-negative duration
                                feature_durations.append((feature_name, start_time.date(), duration))
                        except ValueError:
                            continue # Ignore malformed timestamp

        return {'actions': action_counts, 'sessions': session_durations, 'feature_durations': feature_durations}

    def _update_frequency_cards(self, action_counts):
        """Updates the statistics cards for the frequency tab."""
        # Clear old cards
        while self.stats_cards_freq.count():
            child = self.stats_cards_freq.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        total_actions = sum(action_counts.values())
        most_used = max(action_counts, key=action_counts.get) if action_counts else "N/A"

        self.stats_cards_freq.addWidget(self._create_stat_card("总计操作数", str(total_actions)))
        self.stats_cards_freq.addWidget(self._create_stat_card("最常用功能", most_used))

    def _plot_usage_frequency(self, action_counts):
        """Plots the feature usage frequency as a bar chart."""
        self._update_frequency_cards(action_counts)
        fig = self.frequency_canvas.figure
        ax = self.frequency_canvas.axes
        fig.patch.set_facecolor("#ffffff")
        ax.set_facecolor("#ffffff")
        ax.clear()

        if not action_counts:
            ax.text(0.5, 0.5, '无可用数据', ha='center', va='center', fontsize=12)
            self.frequency_canvas.draw()
            return

        labels = list(action_counts.keys())
        counts = list(action_counts.values())

        ax.bar(labels, counts, color="#3498db")
        ax.set_title('各功能模块使用频率', fontsize=16, weight='bold', color="#2c3e50")
        ax.set_ylabel('使用次数', fontsize=12, color="#34495e")
        ax.tick_params(axis='x', labelrotation=45, labelsize=10, colors="#495057")
        ax.tick_params(axis='y', colors="#495057")

        ax.grid(axis='y', linestyle='--', alpha=0.7, color="#e9ecef")
        for spine in ax.spines.values():
            spine.set_edgecolor("#adb5bd")

        self.frequency_canvas.figure.tight_layout()
        self.frequency_canvas.draw()

    def _update_duration_cards(self, daily_durations):
        """Updates the statistics cards for the duration tab."""
        while self.stats_cards_duration.count():
            child = self.stats_cards_duration.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        total_duration = sum(daily_durations.values())
        avg_duration = total_duration / len(daily_durations) if daily_durations else 0

        self.stats_cards_duration.addWidget(self._create_stat_card("总使用时长 (分钟)", f"{total_duration:.2f}"))
        self.stats_cards_duration.addWidget(self._create_stat_card("平均每日时长 (分钟)", f"{avg_duration:.2f}"))

    def _plot_usage_duration(self, session_durations):
        """Plots the toolbox usage duration as a line chart."""
        fig = self.duration_canvas.figure
        ax = self.duration_canvas.axes
        fig.patch.set_facecolor("#ffffff")
        ax.set_facecolor("#ffffff")
        ax.clear()

        if not session_durations:
            ax.text(0.5, 0.5, '无可用数据', ha='center', va='center', fontsize=12)
            self.duration_canvas.draw()
            return

        # Aggregate durations by date
        daily_durations = defaultdict(float)
        for date_str, duration in session_durations:
            daily_durations[date_str] += duration

        self._update_duration_cards(daily_durations)

        sorted_dates = sorted(daily_durations.keys())
        durations = [daily_durations[date] for date in sorted_dates]

        ax.plot(sorted_dates, durations, marker='o', linestyle='-', color="#e74c3c")
        ax.set_title('每日工具箱使用总时长', fontsize=16, weight='bold', color="#2c3e50")
        ax.set_ylabel('时长 (分钟)', fontsize=12, color="#34495e")
        ax.tick_params(axis='x', labelrotation=45, labelsize=10, colors="#495057")
        ax.tick_params(axis='y', colors="#495057")

        ax.grid(True, linestyle='--', alpha=0.7, color="#e9ecef")
        for spine in ax.spines.values():
            spine.set_edgecolor("#adb5bd")

        self.duration_canvas.figure.tight_layout()
        self.duration_canvas.draw()

    def _plot_feature_duration(self, feature_durations):
        """Plots the single feature usage duration as a bar chart based on the filter."""
        fig = self.feature_duration_canvas.figure
        ax = self.feature_duration_canvas.axes
        fig.patch.set_facecolor("#ffffff")
        ax.set_facecolor("#ffffff")
        ax.clear()

        time_filter = self.feature_duration_filter.currentText()
        today = datetime.now().date()

        filtered_data = defaultdict(float)
        for feature, date_obj, duration in feature_durations:
            if time_filter == "今日" and date_obj == today:
                filtered_data[feature] += duration
            elif time_filter == "本月" and date_obj.year == today.year and date_obj.month == today.month:
                filtered_data[feature] += duration

        if not filtered_data:
            ax.text(0.5, 0.5, '当前时间范围无数据', ha='center', va='center', fontsize=12, color="#495057")
            self.feature_duration_canvas.draw()
            return
            
        # Convert seconds to minutes for better readability
        for feature in filtered_data:
            filtered_data[feature] /= 60

        labels = list(filtered_data.keys())
        durations = list(filtered_data.values())

        ax.barh(labels, durations, color="#3498db") # Horizontal bar chart
        ax.set_title(f'各功能使用时长 ({time_filter})', fontsize=16, weight='bold', color="#2c3e50")
        ax.set_xlabel('使用时长 (分钟)', fontsize=12, color="#34495e")
        ax.tick_params(axis='x', colors="#495057")
        ax.tick_params(axis='y', labelsize=10, colors="#495057")

        ax.grid(axis='x', linestyle='--', alpha=0.7, color="#e9ecef")
        for spine in ax.spines.values():
            spine.set_edgecolor("#adb5bd")

        self.feature_duration_canvas.figure.tight_layout()
        self.feature_duration_canvas.draw()

# --- From log_management.py ---

class SwitcherWidget(QWidget):
    """切换按钮组件"""
    log_view_selected = pyqtSignal()
    statistics_selected = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        self.button_group = QButtonGroup(self)
        self.button_group.setExclusive(True)

        self.log_view_btn = QPushButton("日志查看")
        self.log_view_btn.setCheckable(True)
        self.log_view_btn.setChecked(True)
        self.log_view_btn.toggled.connect(self._on_log_view_toggled)
        layout.addWidget(self.log_view_btn)
        self.button_group.addButton(self.log_view_btn)

        self.statistics_btn = QPushButton("日志统计")
        self.statistics_btn.setCheckable(True)
        self.statistics_btn.toggled.connect(self._on_statistics_toggled)
        layout.addWidget(self.statistics_btn)
        self.button_group.addButton(self.statistics_btn)

        self.update_button_styles()

    def _on_log_view_toggled(self, checked):
        if checked:
            self.update_button_styles()
            self.log_view_selected.emit()

    def _on_statistics_toggled(self, checked):
        if checked:
            self.update_button_styles()
            self.statistics_selected.emit()

    def update_button_styles(self):
        """更新按钮样式以反映当前选择"""
        for button in self.button_group.buttons():
            if button.isChecked():
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: #e3f2fd;
                        color: #1C64F2;
                        border: 1px solid #dcdcdc;
                        border-radius: 6px;
                        padding: 8px 16px;
                        font-weight: bold;
                    }}
                """)
            else:
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: #f5f5f5;
                        color: #333333;
                        border: 1px solid #dcdcdc;
                        border-radius: 6px;
                        padding: 8px 16px;
                    }}
                    QPushButton:hover {{
                        background-color: #e9ecef;
                    }}
                """)

class LogViewerWidget(QWidget):
    """A widget for viewing and searching log files with a modern design."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.log_dir = "logs/toolbox"
        self.init_ui()
        self.load_log_files()

    def init_ui(self):
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 10, 0, 0)
        main_layout.setSpacing(15)

        # Left panel for log file list
        left_panel = QFrame()
        left_panel.setFixedWidth(300)
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0,0,0,0)
        self.log_files_table = QTableWidget()
        self.setup_files_table()
        left_layout.addWidget(self.log_files_table)
        left_panel.setLayout(left_layout)

        # Right panel for log content
        right_panel = QFrame()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(0,0,0,0)

        # Search and Refresh bar
        toolbar_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("在当前日志中搜索...")
        self.search_input.textChanged.connect(self.filter_log_content)
        self.search_input.setStyleSheet(f"""
            QLineEdit {{
                border: 1px solid #ced4da;
                border-radius: 4px;
                padding: 5px;
                background-color: white;
            }}
        """)
        refresh_button = QPushButton("刷新")
        refresh_button.clicked.connect(self.load_log_files)
        toolbar_layout.addWidget(self.search_input)
        toolbar_layout.addWidget(refresh_button)
        right_layout.addLayout(toolbar_layout)

        self.log_content_table = QTableWidget()
        self.setup_content_table()
        right_layout.addWidget(self.log_content_table)
        right_panel.setLayout(right_layout)

        main_layout.addWidget(left_panel)
        main_layout.addWidget(right_panel)
        self.setLayout(main_layout)

    def setup_files_table(self):
        self.log_files_table.setColumnCount(3)
        self.log_files_table.setHorizontalHeaderLabels(["文件名", "大小", "修改日期"])
        self.log_files_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.log_files_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.log_files_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.log_files_table.verticalHeader().hide()
        self.log_files_table.setShowGrid(False)
        self.log_files_table.itemSelectionChanged.connect(self.on_log_file_selected)
        self.log_files_table.setStyleSheet(f"""
            QTableWidget {{
                border: 1px solid #e0e0e0;
                border-radius: 4px;
            }}
            QHeaderView::section {{
                background-color: #f1f3f5;
                font-weight: bold;
                padding: 4px;
                border-bottom: 1px solid #e0e0e0;
            }}
        """)

    def setup_content_table(self):
        self.log_content_table.setColumnCount(2)
        self.log_content_table.setHorizontalHeaderLabels(["时间戳", "事件"])
        self.log_content_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.log_content_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.log_content_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.log_content_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.log_content_table.setAlternatingRowColors(True)
        self.log_content_table.verticalHeader().hide()
        self.log_content_table.setStyleSheet(f"""
            QTableWidget {{
                border: 1px solid #e0e0e0;
                alternate-background-color: #f8f9fa;
            }}
            QHeaderView::section {{
                background-color: #f1f3f5;
                font-weight: bold;
                padding: 4px;
                border-bottom: 1px solid #e0e0e0;
            }}
        """)

    def load_log_files(self):
        self.log_files_table.setRowCount(0)
        if not os.path.exists(self.log_dir):
            return

        files = sorted([f for f in os.listdir(self.log_dir) if f.endswith(".log")], reverse=True)
        self.log_files_table.setRowCount(len(files))
        for i, filename in enumerate(files):
            filepath = os.path.join(self.log_dir, filename)
            stat = os.stat(filepath)
            size_kb = f"{stat.st_size / 1024:.1f} KB"
            mod_time = datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M')

            self.log_files_table.setItem(i, 0, QTableWidgetItem(filename))
            self.log_files_table.setItem(i, 1, QTableWidgetItem(size_kb))
            self.log_files_table.setItem(i, 2, QTableWidgetItem(mod_time))

        if files:
            self.log_files_table.selectRow(0)

    def on_log_file_selected(self):
        selected_items = self.log_files_table.selectedItems()
        if not selected_items:
            return

        filename = selected_items[0].text()
        file_path = os.path.join(self.log_dir, filename)

        self.display_log_content(file_path)

    def display_log_content(self, file_path):
        self.log_content_table.setRowCount(0)
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            self.log_content_table.setRowCount(len(lines))
            for i, line in enumerate(lines):
                match = re.match(r"\[(.*?)\]\s(.*)", line)
                if match:
                    timestamp, event = match.groups()
                    ts_item = QTableWidgetItem(timestamp.strip())
                    event_item = QTableWidgetItem(event.strip())

                    if "工具箱启动" in event:
                        event_item.setForeground(QColor("#28a745"))
                    elif "工具箱关闭" in event:
                        event_item.setForeground(QColor("#dc3545"))
                    elif "点击导航栏" in event:
                        event_item.setForeground(QColor("#007bff"))

                    self.log_content_table.setItem(i, 0, ts_item)
                    self.log_content_table.setItem(i, 1, event_item)
                else:
                    self.log_content_table.setItem(i, 0, QTableWidgetItem("N/A"))
                    self.log_content_table.setItem(i, 1, QTableWidgetItem(line.strip()))
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Could not read log file: {e}")

    def filter_log_content(self, text):
        for i in range(self.log_content_table.rowCount()):
            event_item = self.log_content_table.item(i, 1)
            if event_item:
                is_visible = text.lower() in event_item.text().lower()
                self.log_content_table.setRowHidden(i, not is_visible)

    def refresh_logs(self):
        self.load_log_files()

class LogManagementWidget(QWidget):
    """Main container widget for Log Viewer and Log Statistics."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.init_ui()
        self.setStyleSheet(f"background-color: #f8f9fa;")

    def init_ui(self):
        """Initializes the main layout with switcher and stacked widget."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # Switcher
        self.switcher = SwitcherWidget()
        self.switcher.log_view_selected.connect(self.show_log_viewer)
        self.switcher.statistics_selected.connect(self.show_statistics)
        main_layout.addWidget(self.switcher)

        # Stacked Widget for pages
        self.stacked_widget = QStackedWidget()
        main_layout.addWidget(self.stacked_widget)

        # Pages
        self.log_viewer_widget = LogViewerWidget()
        self.log_statistics_widget = LogStatisticsWidget()

        self.stacked_widget.addWidget(self.log_viewer_widget)
        self.stacked_widget.addWidget(self.log_statistics_widget)

        self.setLayout(main_layout)

    def show_log_viewer(self):
        """Shows the log viewer page."""
        self.stacked_widget.setCurrentWidget(self.log_viewer_widget)
        self.log_viewer_widget.refresh_logs()

    def show_statistics(self):
        """Shows the statistics page."""
        self.stacked_widget.setCurrentWidget(self.log_statistics_widget)
        self.log_statistics_widget.update_statistics()

    def showEvent(self, event):
        """Overrides the show event to refresh data when the widget is shown."""
        super().showEvent(event)
        current_widget = self.stacked_widget.currentWidget()
        if isinstance(current_widget, LogViewerWidget):
            current_widget.refresh_logs()
        elif isinstance(current_widget, LogStatisticsWidget):
            current_widget.update_statistics()

    def refresh(self):
        """A general refresh method that can be called from outside."""
        self.showEvent(None) 