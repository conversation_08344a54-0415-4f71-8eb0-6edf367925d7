import os
import sys
import time
import datetime
import re
from collections import defaultdict, Counter
import json
from functools import partial

# PyQt5导入
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, 
                           QLabel, QLineEdit, QPushButton, QTableWidget, 
                           QTableWidgetItem, QComboBox, QDateEdit, QHeaderView,
                           QSplitter, QFrame, QTextEdit, QFileDialog, QMessageBox,
                           QApplication, QProgressBar, QDialog, QGroupBox, QCheckBox,
                           QScrollArea, QSizePolicy)
from PyQt5.QtCore import Qt, QDate, pyqtSignal, QTimer, QThread, QSize
from PyQt5.QtGui import QColor, QFont

# Matplotlib导入（用于数据可视化）
import matplotlib
matplotlib.use('Qt5Agg')
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.pyplot as plt
import numpy as np

# 颜色配置已内联到代码中

#------------------------------------------------------
# 日志记录器部分 (原logger.py)
#------------------------------------------------------

class ToolboxLogger:
    """
    工具箱日志记录器，负责创建和写入日志文件
    """
    def __init__(self, log_dir="logs"):
        """
        初始化日志记录器
        
        参数:
            log_dir (str): 日志文件存放目录
        """
        # 确保日志目录存在
        self.log_dir = log_dir
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
            
        # 创建基于日期的日志文件名
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        self.log_file = os.path.join(self.log_dir, f"toolbox_log_{today}.log")
        
        # 记录工具箱启动事件
        self.log_start()
        
    def _write_log(self, message_type, message, details=""):
        """
        向日志文件写入一条日志
        
        参数:
            message_type (str): 消息类型，如 START, STOP, ACTION, ERROR 等
            message (str): 日志消息内容
            details (str): 附加详细信息
        """
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 构造日志条目
        log_entry = f"{timestamp} | {message_type} | {message}"
        if details:
            log_entry += f" | {details}"
            
        # 写入日志文件
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry + "\n")
            
    def log_start(self):
        """记录程序启动事件"""
        self._write_log("START", "工具箱程序启动")
        
    def log_stop(self):
        """记录程序关闭事件"""
        self._write_log("STOP", "工具箱程序关闭")
        
    def log_action(self, action, module="", details=""):
        """
        记录用户操作
        
        参数:
            action (str): 用户执行的操作
            module (str): 操作所在的模块
            details (str): 操作的详细信息
        """
        message = f"{action}"
        if module:
            message = f"{module} - {message}"
        self._write_log("ACTION", message, details)
        
    def log_error(self, error_message, module="", exception=None):
        """
        记录错误信息
        
        参数:
            error_message (str): 错误消息
            module (str): 发生错误的模块
            exception (Exception): 异常对象
        """
        message = error_message
        if module:
            message = f"{module} - {message}"
            
        details = str(exception) if exception else ""
        self._write_log("ERROR", message, details)
        
    def log_info(self, info_message, module=""):
        """
        记录一般信息
        
        参数:
            info_message (str): 信息内容
            module (str): 相关模块
        """
        message = info_message
        if module:
            message = f"{module} - {message}"
        self._write_log("INFO", message)
        
    def log_success(self, success_message, module="", details=""):
        """
        记录成功操作
        
        参数:
            success_message (str): 成功消息
            module (str): 相关模块
            details (str): 详细信息
        """
        message = success_message
        if module:
            message = f"{module} - {message}"
        self._write_log("SUCCESS", message, details)

# 创建全局日志记录器实例
logger = ToolboxLogger()

#------------------------------------------------------
# 日志统计部分 (原log_statistics.py)
#------------------------------------------------------

class LogStatistics:
    """日志统计分析类，用于从日志文件中提取和分析数据"""
    
    def __init__(self, log_dir="logs"):
        """
        初始化日志统计分析器
        
        参数:
            log_dir (str): 日志文件目录
        """
        self.log_dir = log_dir
        
    def get_available_logs(self):
        """
        获取可用的日志文件列表
        
        返回:
            list: 日志文件名列表
        """
        if not os.path.exists(self.log_dir):
            return []
            
        log_files = [f for f in os.listdir(self.log_dir) 
                    if f.startswith("toolbox_log_") and f.endswith(".log")]
        return sorted(log_files, reverse=True)  # 最新的日志排在前面
    
    def parse_log_file(self, log_file):
        """
        解析日志文件内容
        
        参数:
            log_file (str): 日志文件路径
            
        返回:
            list: 日志条目字典列表
        """
        entries = []
        full_path = os.path.join(self.log_dir, log_file) if not os.path.isabs(log_file) else log_file
        
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                        
                    # 解析日志条目
                    parts = [part.strip() for part in line.split('|')]
                    if len(parts) >= 3:
                        entry = {
                            'timestamp': parts[0],
                            'type': parts[1],
                            'message': parts[2],
                            'details': parts[3] if len(parts) > 3 else ''
                        }
                        entries.append(entry)
        except Exception as e:
            print(f"解析日志文件出错: {str(e)}")
            
        return entries
        
    def calculate_usage_stats(self, entries):
        """
        计算使用统计数据
        
        参数:
            entries (list): 日志条目列表
            
        返回:
            dict: 包含各种统计信息的字典
        """
        stats = {
            'total_actions': 0,
            'actions_by_module': defaultdict(int),
            'errors': 0,
            'usage_time': 0,  # 分钟
            'most_used_features': [],
            'sessions': 0,
            'start_times': [],
            'stop_times': []
        }
        
        # 提取会话信息（启动和关闭时间）
        for entry in entries:
            if entry['type'] == 'START':
                stats['sessions'] += 1
                stats['start_times'].append(entry['timestamp'])
            elif entry['type'] == 'STOP':
                stats['stop_times'].append(entry['timestamp'])
                
            if entry['type'] == 'ACTION':
                stats['total_actions'] += 1
                
                # 尝试提取模块名
                message = entry['message']
                module = message.split(' - ')[0] if ' - ' in message else '未知模块'
                stats['actions_by_module'][module] += 1
                
            elif entry['type'] == 'ERROR':
                stats['errors'] += 1
        
        # 计算使用时长
        if stats['start_times'] and stats['stop_times']:
            for i in range(min(len(stats['start_times']), len(stats['stop_times']))):
                try:
                    start_time = datetime.datetime.strptime(stats['start_times'][i], '%Y-%m-%d %H:%M:%S')
                    stop_time = datetime.datetime.strptime(stats['stop_times'][i], '%Y-%m-%d %H:%M:%S')
                    delta = stop_time - start_time
                    stats['usage_time'] += delta.total_seconds() / 60  # 转换为分钟
                except ValueError:
                    pass
        
        # 最常用功能
        if stats['actions_by_module']:
            most_common = Counter(stats['actions_by_module']).most_common(5)
            stats['most_used_features'] = most_common
            
        return stats
        
    def get_activity_by_date(self, entries):
        """
        按日期统计活动
        
        参数:
            entries (list): 日志条目列表
            
        返回:
            dict: 以日期为键，活动计数为值的字典
        """
        activity = defaultdict(int)
        
        for entry in entries:
            try:
                date = entry['timestamp'].split()[0]  # 提取日期部分
                activity[date] += 1
            except (KeyError, IndexError):
                pass
                
        return dict(sorted(activity.items()))

class MatplotlibCanvas(FigureCanvas):
    """Matplotlib画布类，用于在Qt界面中显示统计图表"""
    
    def __init__(self, parent=None, width=6, height=4, dpi=100):
        """
        初始化Matplotlib画布
        
        参数:
            parent: 父部件
            width (float): 画布宽度（英寸）
            height (float): 画布高度（英寸）
            dpi (int): 分辨率
        """
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        self.fig.set_facecolor('white')
        FigureCanvas.__init__(self, self.fig)
        self.setParent(parent)
        
        FigureCanvas.setSizePolicy(self,
                                  QSizePolicy.Expanding,
                                  QSizePolicy.Expanding)
        FigureCanvas.updateGeometry(self)
        
    def clear(self):
        """清空画布"""
        self.fig.clear()

class StatisticsWidget(QWidget):
    """日志统计显示窗口部件"""
    
    def __init__(self, parent=None):
        """
        初始化统计窗口
        
        参数:
            parent: 父部件
        """
        super(StatisticsWidget, self).__init__(parent)
        self.stats_analyzer = LogStatistics()
        
        self.initUI()
        
    def initUI(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 顶部控制区域
        control_layout = QHBoxLayout()
        
        # 日志选择
        self.log_combo = QComboBox()
        self.log_combo.addItems(self.stats_analyzer.get_available_logs())
        self.log_combo.currentIndexChanged.connect(self.update_statistics)
        control_layout.addWidget(QLabel("选择日志文件:"))
        control_layout.addWidget(self.log_combo)
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_logs)
        control_layout.addWidget(refresh_btn)
        
        control_layout.addStretch(1)
        layout.addLayout(control_layout)
        
        # 创建选项卡小部件
        self.tabs = QTabWidget()
        
        # 功能使用统计选项卡
        self.features_tab = QWidget()
        features_layout = QVBoxLayout(self.features_tab)
        
        # 添加功能使用统计图表
        self.features_canvas = MatplotlibCanvas(self.features_tab, width=6, height=4)
        features_layout.addWidget(self.features_canvas)
        
        # 使用时长选项卡
        self.usage_tab = QWidget()
        usage_layout = QVBoxLayout(self.usage_tab)
        
        # 添加使用时长图表
        self.usage_canvas = MatplotlibCanvas(self.usage_tab, width=6, height=4)
        usage_layout.addWidget(self.usage_canvas)
        
        # 添加选项卡
        self.tabs.addTab(self.features_tab, "功能使用统计")
        self.tabs.addTab(self.usage_tab, "使用时长统计")
        
        layout.addWidget(self.tabs)
        
        # 统计信息区域
        self.stats_info = QTextEdit()
        self.stats_info.setReadOnly(True)
        self.stats_info.setMaximumHeight(100)
        layout.addWidget(self.stats_info)
        
        # 初始加载统计
        if self.log_combo.count() > 0:
            self.update_statistics()
    
    def refresh_logs(self):
        """刷新可用日志文件列表"""
        current_text = self.log_combo.currentText()
        
        self.log_combo.clear()
        log_files = self.stats_analyzer.get_available_logs()
        self.log_combo.addItems(log_files)
        
        # 尝试恢复之前的选择
        index = self.log_combo.findText(current_text)
        if index >= 0:
            self.log_combo.setCurrentIndex(index)
        elif self.log_combo.count() > 0:
            self.log_combo.setCurrentIndex(0)
            
        self.update_statistics()
        
    def update_statistics(self):
        """更新统计图表和信息"""
        if self.log_combo.count() == 0:
            return
            
        selected_log = self.log_combo.currentText()
        entries = self.stats_analyzer.parse_log_file(selected_log)
        
        if not entries:
            self.stats_info.setText("没有找到日志数据")
            return
            
        # 计算统计数据
        stats = self.stats_analyzer.calculate_usage_stats(entries)
        activity_by_date = self.stats_analyzer.get_activity_by_date(entries)
        
        # 更新功能使用统计图
        self.update_features_chart(stats)
        
        # 更新使用时长图
        self.update_usage_chart(activity_by_date)
        
        # 更新统计信息
        info_text = f"总操作次数: {stats['total_actions']}\n"
        info_text += f"累计使用时长: {stats['usage_time']:.1f} 分钟\n"
        info_text += f"错误次数: {stats['errors']}\n"
        
        if stats['most_used_features']:
            top_feature, top_count = stats['most_used_features'][0]
            info_text += f"最常用功能: {top_feature} ({top_count}次)"
            
        self.stats_info.setText(info_text)
        
    def update_features_chart(self, stats):
        """
        更新功能使用统计图表
        
        参数:
            stats (dict): 统计数据
        """
        self.features_canvas.clear()
        ax = self.features_canvas.fig.add_subplot(111)
        
        modules = []
        counts = []
        
        for module, count in stats.get('actions_by_module', {}).items():
            if count > 0:  # 只显示有使用的功能
                modules.append(module)
                counts.append(count)
                
        # 如果没有数据，不显示图表
        if not modules:
            ax.set_title("没有功能使用数据")
            self.features_canvas.draw()
            return
            
        # 排序，确保最常用的功能在顶部
        modules, counts = zip(*sorted(zip(modules, counts), key=lambda x: x[1]))
        
        # 创建水平条形图
        y_pos = np.arange(len(modules))
        ax.barh(y_pos, counts, align='center', color="#3498db")
        ax.set_yticks(y_pos)
        ax.set_yticklabels(modules)
        ax.invert_yaxis()  # 最常用的在顶部
        ax.set_xlabel('使用次数')
        ax.set_title('功能使用频率')
        
        # 设置坐标轴颜色等
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['bottom'].set_color("#adb5bd")
        ax.spines['left'].set_color("#adb5bd")
        ax.tick_params(axis='x', colors="#495057")
        ax.tick_params(axis='y', colors="#495057")
        
        self.features_canvas.fig.tight_layout()
        self.features_canvas.draw()
        
    def update_usage_chart(self, activity_by_date):
        """
        更新使用时长统计图表
        
        参数:
            activity_by_date (dict): 按日期统计的活动数据
        """
        self.usage_canvas.clear()
        ax = self.usage_canvas.fig.add_subplot(111)
        
        if not activity_by_date:
            ax.set_title("没有使用时长数据")
            self.usage_canvas.draw()
            return
            
        dates = list(activity_by_date.keys())
        activities = list(activity_by_date.values())
        
        # 创建折线图
        ax.plot(dates, activities, marker='o', linestyle='-', color="#e74c3c")
        
        # 设置标签
        ax.set_xlabel('日期')
        ax.set_ylabel('活动次数')
        ax.set_title('日志活动趋势')
        
        # 旋转x轴标签以避免重叠
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')
        
        # 设置坐标轴颜色等
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['bottom'].set_color("#adb5bd")
        ax.spines['left'].set_color("#adb5bd")
        ax.tick_params(axis='x', colors="#495057")
        ax.tick_params(axis='y', colors="#495057")
        ax.grid(True, linestyle='--', alpha=0.7, color="#e9ecef")
        
        self.usage_canvas.fig.tight_layout()
        self.usage_canvas.draw()

#------------------------------------------------------
# 日志管理界面部分 (原log_management.py)
#------------------------------------------------------

class LogViewerWidget(QWidget):
    """日志查看器小部件，用于显示原始日志内容"""
    
    def __init__(self, parent=None):
        """
        初始化日志查看器
        
        参数:
            parent: 父部件
        """
        super(LogViewerWidget, self).__init__(parent)
        self.log_dir = "logs"
        self.initUI()
        
    def initUI(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 顶部控制面板
        control_panel = QHBoxLayout()
        
        # 日志文件选择
        self.log_selector = QComboBox()
        self.refresh_log_files()
        control_panel.addWidget(QLabel("日志文件:"))
        control_panel.addWidget(self.log_selector)
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_log_files)
        control_panel.addWidget(refresh_btn)
        
        # 搜索功能
        control_panel.addWidget(QLabel("搜索:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入搜索关键词...")
        self.search_input.textChanged.connect(self.filter_logs)
        control_panel.addWidget(self.search_input)
        
        # 类型过滤
        control_panel.addWidget(QLabel("类型:"))
        self.type_filter = QComboBox()
        self.type_filter.addItems(["全部", "START", "STOP", "ACTION", "ERROR", "INFO", "SUCCESS"])
        self.type_filter.currentTextChanged.connect(self.filter_logs)
        control_panel.addWidget(self.type_filter)
        
        # 添加控制面板
        layout.addLayout(control_panel)
        
        # 日志表格
        self.log_table = QTableWidget()
        self.log_table.setColumnCount(4)
        self.log_table.setHorizontalHeaderLabels(["时间戳", "类型", "消息", "详细信息"])
        self.log_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.log_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.log_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.log_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Stretch)
        
        # 设置表格样式
        header_style = f"""
            QHeaderView::section {{
                background-color: #f1f3f5;
                color: #34495e;
                font-weight: bold;
                padding: 6px;
                border: 1px solid #e0e0e0;
            }}
        """
        self.log_table.horizontalHeader().setStyleSheet(header_style)
        
        layout.addWidget(self.log_table)
        
        # 日志文件选择变化时加载日志
        self.log_selector.currentIndexChanged.connect(self.load_selected_log)
        
        # 第一次加载
        if self.log_selector.count() > 0:
            self.load_selected_log()
            
    def refresh_log_files(self):
        """刷新可用日志文件列表"""
        current_text = self.log_selector.currentText()
        self.log_selector.clear()
        
        # 获取日志文件列表
        try:
            if os.path.exists(self.log_dir):
                logs = [f for f in os.listdir(self.log_dir) 
                        if f.startswith("toolbox_log_") and f.endswith(".log")]
                logs.sort(reverse=True)  # 最新的日志排在前面
                self.log_selector.addItems(logs)
                
                # 恢复之前的选择或选择最新的
                index = self.log_selector.findText(current_text)
                if index >= 0:
                    self.log_selector.setCurrentIndex(index)
                elif self.log_selector.count() > 0:
                    self.log_selector.setCurrentIndex(0)
        except Exception as e:
            QMessageBox.warning(self, "错误", f"获取日志文件列表失败: {str(e)}")
            
    def load_selected_log(self):
        """加载选中的日志文件"""
        if self.log_selector.count() == 0:
            return
            
        log_file = self.log_selector.currentText()
        self.load_log_file(os.path.join(self.log_dir, log_file))
        
    def load_log_file(self, file_path):
        """
        加载日志文件内容到表格
        
        参数:
            file_path (str): 日志文件路径
        """
        self.log_table.setRowCount(0)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.entries = []
                
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                        
                    parts = [part.strip() for part in line.split('|')]
                    if len(parts) >= 3:
                        entry = {
                            'timestamp': parts[0],
                            'type': parts[1],
                            'message': parts[2],
                            'details': parts[3] if len(parts) > 3 else ''
                        }
                        self.entries.append(entry)
                        
            # 显示所有日志条目
            self.filter_logs()
                
        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载日志文件失败: {str(e)}")
            
    def filter_logs(self):
        """根据搜索和类型过滤日志条目"""
        self.log_table.setRowCount(0)
        
        if not hasattr(self, 'entries'):
            return
            
        search_text = self.search_input.text().lower()
        type_filter = self.type_filter.currentText()
        
        row = 0
        for entry in self.entries:
            # 检查类型过滤
            if type_filter != "全部" and entry['type'] != type_filter:
                continue
                
            # 检查搜索文本
            if search_text and not (
                search_text in entry['timestamp'].lower() or
                search_text in entry['type'].lower() or
                search_text in entry['message'].lower() or
                search_text in entry['details'].lower()):
                continue
                
            # 通过筛选，添加到表格
            self.log_table.insertRow(row)
            
            # 设置单元格
            timestamp_item = QTableWidgetItem(entry['timestamp'])
            self.log_table.setItem(row, 0, timestamp_item)
            
            type_item = QTableWidgetItem(entry['type'])
            
            # 根据日志类型设置颜色
            if entry['type'] == 'ERROR':
                type_item.setForeground(QColor("#dc3545"))
            elif entry['type'] == 'START':
                type_item.setForeground(QColor("#28a745"))
            elif entry['type'] == 'ACTION':
                type_item.setForeground(QColor("#007bff"))
                
            self.log_table.setItem(row, 1, type_item)
            
            self.log_table.setItem(row, 2, QTableWidgetItem(entry['message']))
            self.log_table.setItem(row, 3, QTableWidgetItem(entry['details']))
            
            # 交替行颜色
            if row % 2 == 1:
                for col in range(4):
                    self.log_table.item(row, col).setBackground(QColor("#f8f9fa"))
                    
            row += 1

class LogManagementWidget(QWidget):
    """日志管理主界面，整合日志查看和统计功能"""
    
    def __init__(self, parent=None):
        """
        初始化日志管理界面
        
        参数:
            parent: 父部件
        """
        super(LogManagementWidget, self).__init__(parent)
        self.setStyleSheet(f"background-color: #f8f9fa;")
        self.initUI()
        
    def initUI(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 页面标题
        title = QLabel("日志管理系统")
        title.setStyleSheet(f"""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 创建选项卡部件
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid #dcdcdc;
                background-color: #ffffff;
            }}
            QTabBar::tab {{
                background-color: #f5f5f5;
                color: #333333;
                padding: 8px 15px;
                border: 1px solid #dcdcdc;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                margin-right: 2px;
            }}
            QTabBar::tab:selected {{
                background-color: #ffffff;
                color: #1C64F2;
                border-bottom: none;
            }}
        """)
        
        # 日志查看选项卡
        self.log_viewer = LogViewerWidget()
        self.tabs.addTab(self.log_viewer, "日志查看")
        
        # 统计分析选项卡
        self.stats_widget = StatisticsWidget()
        self.tabs.addTab(self.stats_widget, "日志统计")
        
        layout.addWidget(self.tabs)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        # 导出按钮
        export_btn = QPushButton("导出日志")
        export_btn.clicked.connect(self.export_log)
        button_layout.addWidget(export_btn)
        
        # 帮助按钮
        help_btn = QPushButton("帮助")
        help_btn.clicked.connect(self.show_help)
        button_layout.addWidget(help_btn)
        
        button_layout.addStretch(1)
        layout.addLayout(button_layout)
        
    def export_log(self):
        """导出当前选择的日志文件"""
        if self.tabs.currentIndex() == 0:  # 日志查看选项卡
            if self.log_viewer.log_selector.count() == 0:
                QMessageBox.warning(self, "导出失败", "没有可用的日志文件")
                return
                
            log_file = self.log_viewer.log_selector.currentText()
            source_path = os.path.join(self.log_viewer.log_dir, log_file)
            
            if not os.path.exists(source_path):
                QMessageBox.warning(self, "导出失败", f"日志文件不存在: {source_path}")
                return
                
            # 选择保存位置
            file_dialog = QFileDialog()
            file_dialog.setAcceptMode(QFileDialog.AcceptSave)
            file_dialog.setDefaultSuffix("log")
            file_dialog.selectFile(log_file)
            
            if file_dialog.exec_():
                dest_path = file_dialog.selectedFiles()[0]
                try:
                    # 复制文件
                    import shutil
                    shutil.copy2(source_path, dest_path)
                    QMessageBox.information(self, "导出成功", f"日志已导出至: {dest_path}")
                except Exception as e:
                    QMessageBox.critical(self, "导出失败", f"导出日志时出错: {str(e)}")
        else:
            QMessageBox.information(self, "提示", "请切换到日志查看选项卡以导出日志文件")
            
    def show_help(self):
        """显示帮助信息"""
        help_text = """
        <h3>日志管理系统帮助</h3>
        
        <p><b>日志查看</b></p>
        <ul>
            <li>选择日志文件：从下拉框中选择要查看的日志文件</li>
            <li>刷新：重新加载可用的日志文件列表</li>
            <li>搜索：在日志内容中搜索关键词</li>
            <li>类型过滤：根据日志类型过滤显示内容</li>
        </ul>
        
        <p><b>日志统计</b></p>
        <ul>
            <li>功能使用统计：查看各功能模块的使用频率</li>
            <li>使用时长统计：查看系统使用时长和活动趋势</li>
            <li>刷新：更新日志文件列表和统计数据</li>
        </ul>
        
        <p><b>导出日志</b>: 将当前日志文件导出到指定位置</p>
        """
        
        msg_box = QMessageBox()
        msg_box.setWindowTitle("日志管理系统帮助")
        msg_box.setTextFormat(Qt.RichText)
        msg_box.setText(help_text)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.exec_()
