import os
import json
import sys
import webbrowser
import subprocess
import time

# 导入配置和缓存系统
from startup_cache import startup_cache

# PyQt5 核心组件
from PyQt5.QtWidgets import (QMainWindow, QWidget, QHBoxLayout, QVBoxLayout,
                           QListWidget, QGridLayout, QPushButton,
                           QLabel, QMessageBox, QFrame, QScrollArea,
                           QApplication, QFileIconProvider,
                           QSizePolicy, QGraphicsDropShadowEffect,
                           QTableWidget, QTableWidgetItem, QHeaderView)
from PyQt5.QtCore import Qt, QSize, QPoint, QFileInfo, QUrl, QTimer, QEvent, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QIcon, QColor, QDesktopServices, QFont, QPixmap

# Web引擎组件（可选）
try:
    from PyQt5.QtWebEngineWidgets import QWebEngineView
    WEB_ENGINE_AVAILABLE = True
except ImportError:
    WEB_ENGINE_AVAILABLE = False
    QWebEngineView = None

# 自定义组件
from vehicle_status import VehicleStatusWidget
from chat_dialog import ChatDialog
from fault_query import MainApplicationWindow
from home_page import HomePageWidget

from chat_robot_widget import ChatRobotWidget
from settings_page import SettingsPage
from log_management import get_logger, LogManagementWidget
from community_widget import CommunityWidget

# 网络请求库（可选）
try:
    import requests
except ImportError:
    requests = None

# ==================== 工具类和常量 ====================
class UIUtils:
    """UI工具类 - 封装常用的UI创建和操作函数"""

    @staticmethod
    def create_shadow_effect(blur_radius=15, color=None, offset_x=0, offset_y=5):
        """创建阴影效果的通用函数"""
        try:
            shadow = QGraphicsDropShadowEffect()
            shadow.setBlurRadius(blur_radius)
            if color:
                shadow.setColor(color)
            else:
                shadow.setColor(QColor(59, 130, 246, 60))
            shadow.setOffset(offset_x, offset_y)
            return shadow
        except:
            return None

    @staticmethod
    def delayed_scroll_to_position(scroll_area, position, delay=100):
        """延迟滚动到指定位置的通用函数"""
        QTimer.singleShot(delay, lambda: scroll_area.verticalScrollBar().setValue(position))

    @staticmethod
    def show_delayed_message(parent, msg_type, title, message):
        """延迟显示消息框的通用函数"""
        message_types = {
            "info": lambda: QMessageBox.information(parent, title, message),
            "warning": lambda: QMessageBox.warning(parent, title, message),
            "error": lambda: QMessageBox.critical(parent, title, message)
        }
        if msg_type in message_types:
            QTimer.singleShot(0, message_types[msg_type])

    @staticmethod
    def create_styled_button(text, style_config=None, size=None):
        """创建带样式的按钮"""
        button = QPushButton(text)
        if size:
            button.setFixedSize(*size)
        if style_config:
            button.setStyleSheet(style_config)
        return button

    @staticmethod
    def create_styled_label(text, style_config=None, alignment=None):
        """创建带样式的标签"""
        label = QLabel(text)
        if alignment:
            label.setAlignment(alignment)
        if style_config:
            label.setStyleSheet(style_config)
        return label

    @staticmethod
    def create_grid_layout(spacing=0, margins=(0, 0, 0, 0), alignment=None):
        """创建网格布局"""
        layout = QGridLayout()
        layout.setSpacing(spacing)
        layout.setContentsMargins(*margins)
        if alignment:
            layout.setAlignment(alignment)
        return layout

    @staticmethod
    def create_frame_with_style(style_config, object_name=None):
        """创建带样式的框架"""
        frame = QFrame()
        if object_name:
            frame.setObjectName(object_name)
        frame.setStyleSheet(style_config)
        return frame

# 样式配置类
class StyleConfig:
    """样式配置类 - 集中管理常用样式"""

    # 通用滚动条样式
    HIDDEN_SCROLLBAR = """
        QScrollBar:vertical {
            width: 0px;
            background: transparent;
        }
        QScrollBar:horizontal {
            height: 0px;
            background: transparent;
        }
    """

    # 按钮样式模板
    BUTTON_STYLE_TEMPLATE = """
        QPushButton {{
            background-color: {bg_color};
            color: {text_color};
            font-family: {font_family};
            font-size: {font_size};
            border-radius: {border_radius};
            padding: {padding};
            border: {border};
        }}
        QPushButton:hover {{
            background-color: {hover_bg};
        }}
        QPushButton:pressed {{
            background-color: {pressed_bg};
        }}
    """

    @classmethod
    def get_button_style(cls, bg_color="#3498db", text_color="white",
                        font_family="Microsoft YaHei", font_size="14px",
                        border_radius="3px", padding="3px", border="none",
                        hover_bg="#2980b9", pressed_bg="#21618c"):
        """获取按钮样式"""
        return cls.BUTTON_STYLE_TEMPLATE.format(
            bg_color=bg_color, text_color=text_color, font_family=font_family,
            font_size=font_size, border_radius=border_radius, padding=padding,
            border=border, hover_bg=hover_bg, pressed_bg=pressed_bg
        )

# 常量定义
DEFAULT_MAX_COLS = 6  # 默认每行显示的列数

# 自适应布局常量
class AdaptiveConstants:
    """自适应布局常量"""
    # 应用卡片尺寸 - 基于窗口状态自适应
    CARD_SIZE_MAXIMIZED = (260, 200)      # 最大化时的卡片尺寸
    CARD_SIZE_RESTORED = (220, 170)       # 恢复窗口时的卡片尺寸

    # 按钮尺寸 - 基于窗口状态自适应
    BUTTON_SIZE_MAXIMIZED = (240, 180)    # 最大化时的按钮尺寸
    BUTTON_SIZE_RESTORED = (200, 150)     # 恢复窗口时的按钮尺寸

    # 文件浏览器尺寸
    FILE_SIZE_MAXIMIZED = (150, 140)      # 最大化时的文件框尺寸
    FILE_SIZE_RESTORED = (130, 120)       # 恢复窗口时的文件框尺寸

    # 列数配置
    COLS_MAXIMIZED = 6                    # 最大化时的列数
    COLS_RESTORED = 4                     # 恢复窗口时的列数

    # 间距配置
    SPACING_MAXIMIZED = 20                # 最大化时的间距
    SPACING_RESTORED = 15                 # 恢复窗口时的间距

# 自适应布局管理器
class AdaptiveLayoutManager:
    """自适应布局管理器 - 根据窗口状态调整组件尺寸"""

    def __init__(self, main_window):
        self.main_window = main_window
        self.current_state = "restored"  # 当前状态：maximized 或 restored

    def update_window_state(self, is_maximized):
        """更新窗口状态"""
        new_state = "maximized" if is_maximized else "restored"
        if new_state != self.current_state:
            self.current_state = new_state
            self._apply_adaptive_layout()

    def get_card_size(self):
        """获取当前状态下的卡片尺寸"""
        if self.current_state == "maximized":
            return AdaptiveConstants.CARD_SIZE_MAXIMIZED
        else:
            return AdaptiveConstants.CARD_SIZE_RESTORED

    def get_button_size(self):
        """获取当前状态下的按钮尺寸"""
        if self.current_state == "maximized":
            return AdaptiveConstants.BUTTON_SIZE_MAXIMIZED
        else:
            return AdaptiveConstants.BUTTON_SIZE_RESTORED

    def get_file_size(self):
        """获取当前状态下的文件框尺寸"""
        if self.current_state == "maximized":
            return AdaptiveConstants.FILE_SIZE_MAXIMIZED
        else:
            return AdaptiveConstants.FILE_SIZE_RESTORED

    def get_max_cols(self):
        """获取当前状态下的最大列数"""
        if self.current_state == "maximized":
            return AdaptiveConstants.COLS_MAXIMIZED
        else:
            return AdaptiveConstants.COLS_RESTORED

    def get_spacing(self):
        """获取当前状态下的间距"""
        if self.current_state == "maximized":
            return AdaptiveConstants.SPACING_MAXIMIZED
        else:
            return AdaptiveConstants.SPACING_RESTORED

    def _apply_adaptive_layout(self):
        """应用自适应布局"""
        # 更新主窗口的列数
        self.main_window.max_cols = self.get_max_cols()

        # 更新所有现有的AppFrame组件
        self._update_existing_components()

        # 如果当前显示的是应用列表，重新布局
        if (hasattr(self.main_window, 'current_category') and
            self.main_window.current_category and
            not DecisionMapper.is_special_category(self.main_window.current_category)):
            self.main_window.display_single_category(self.main_window.current_category)

    def _update_existing_components(self):
        """更新所有现有组件的尺寸"""
        # 查找并更新所有AppFrame组件
        if hasattr(self.main_window, 'scroll_content'):
            app_frames = self.main_window.scroll_content.findChildren(AppFrame)
            for frame in app_frames:
                frame.adaptive_manager = self
                frame.update_adaptive_size()

        # 查找并更新所有FileExplorerFrame组件
        if hasattr(self.main_window, 'scroll_content'):
            file_frames = self.main_window.scroll_content.findChildren(FileExplorerFrame)
            for frame in file_frames:
                frame.adaptive_manager = self
                frame.update_adaptive_size()

        # 更新智能卡片容器（如果存在）
        if hasattr(self.main_window, 'scroll_content'):
            smart_containers = self.main_window.scroll_content.findChildren(SmartCardContainer)
            for container in smart_containers:
                container.adaptive_manager = self
                container.update_adaptive_layout()

        # 更新统计卡片（如果存在）
        from home_page import StatisticsCard
        if hasattr(self.main_window, 'scroll_content'):
            stats_cards = self.main_window.scroll_content.findChildren(StatisticsCard)
            for card in stats_cards:
                card.adaptive_manager = self
                card.update_adaptive_size()

        # 更新AI对话框尺寸（如果存在）
        if hasattr(self.main_window, 'chat_dialog') and self.main_window.chat_dialog:
            self.main_window.chat_dialog.update_adaptive_size()

        # 更新流程生成助手尺寸（如果存在）
        if hasattr(self.main_window, 'chat_robot_widget') and self.main_window.chat_robot_widget:
            self.main_window.chat_robot_widget.adaptive_manager = self
            self.main_window.chat_robot_widget.update_adaptive_size()

        # 更新基础检查面板尺寸（如果存在）
        self._update_check_panels_adaptive_size()

    def _update_check_panels_adaptive_size(self):
        """更新基础检查面板的自适应尺寸"""
        if not hasattr(self.main_window, 'scroll_content'):
            return

        # 查找所有检查面板
        check_items_panels = self.main_window.scroll_content.findChildren(QWidget, "check_items_panel")
        check_content_panels = self.main_window.scroll_content.findChildren(QWidget, "check_content_panel")
        check_standards_panels = self.main_window.scroll_content.findChildren(QWidget, "check_standards_panel")

        # 根据窗口状态设置自适应尺寸
        is_maximized = self.current_state == "maximized"

        # 更新检查项目面板
        for panel in check_items_panels:
            if is_maximized:
                panel.setMaximumWidth(600)
                panel.setMinimumWidth(250)
            else:
                panel.setMaximumWidth(450)
                panel.setMinimumWidth(180)

        # 更新检查细则面板
        for panel in check_content_panels:
            if is_maximized:
                panel.setMaximumWidth(850)
                panel.setMinimumWidth(900)
            else:
                panel.setMaximumWidth(600)
                panel.setMinimumWidth(650)

        # 更新相关标准面板
        for panel in check_standards_panels:
            if is_maximized:
                panel.setMaximumWidth(500)
                panel.setMinimumWidth(550)
            else:
                panel.setMaximumWidth(350)
                panel.setMinimumWidth(400)

# 向后兼容的函数别名
def create_shadow_effect(*args, **kwargs):
    return UIUtils.create_shadow_effect(*args, **kwargs)

def delayed_scroll_to_position(*args, **kwargs):
    return UIUtils.delayed_scroll_to_position(*args, **kwargs)

def show_delayed_message(*args, **kwargs):
    return UIUtils.show_delayed_message(*args, **kwargs)

# 数据处理工具类
class DataUtils:
    """数据处理工具类 - 封装常用的数据处理函数"""

    @staticmethod
    def validate_app_data(app_data):
        """验证应用数据的有效性"""
        required_fields = ['name', 'path']
        return all(field in app_data and app_data[field] for field in required_fields)

    @staticmethod
    def filter_valid_apps(apps_list):
        """过滤有效的应用数据"""
        return [app for app in apps_list if DataUtils.validate_app_data(app)]

    @staticmethod
    def group_apps_by_category(apps_data):
        """按分类分组应用数据"""
        grouped = {}
        for category, apps in apps_data.items():
            if isinstance(apps, list):
                grouped[category] = DataUtils.filter_valid_apps(apps)
            elif isinstance(apps, dict):
                grouped[category] = {
                    subcategory: DataUtils.filter_valid_apps(subapps)
                    for subcategory, subapps in apps.items()
                    if isinstance(subapps, list)
                }
        return grouped

    @staticmethod
    def get_app_icon_path(app_data, default_icon="../image/default_icon.png"):
        """获取应用图标路径"""
        return app_data.get('icon', default_icon)

    @staticmethod
    def get_app_description(app_data, default_desc="暂无描述"):
        """获取应用描述"""
        return app_data.get('description', default_desc)

# 事件处理工具类
class EventUtils:
    """事件处理工具类 - 封装常用的事件处理逻辑"""

    @staticmethod
    def setup_click_timer(widget, single_click_callback, double_click_interval=None):
        """设置点击定时器"""
        if not double_click_interval:
            double_click_interval = QApplication.doubleClickInterval()

        timer = QTimer()
        timer.setSingleShot(True)
        timer.timeout.connect(single_click_callback)

        def handle_click():
            timer.start(double_click_interval)

        widget.clicked.connect(handle_click)
        return timer

    @staticmethod
    def setup_tooltip_timer(widget, tooltip_callback, delay=300):
        """设置工具提示定时器"""
        timer = QTimer()
        timer.setSingleShot(True)
        timer.timeout.connect(tooltip_callback)

        def on_enter():
            timer.start(delay)

        def on_leave():
            timer.stop()

        widget.enterEvent = lambda _: on_enter()  # 使用_表示未使用的参数
        widget.leaveEvent = lambda _: on_leave()  # 使用_表示未使用的参数
        return timer

    @staticmethod
    def create_debounced_handler(callback, delay=300):
        """创建防抖处理器"""
        timer = QTimer()
        timer.setSingleShot(True)
        timer.timeout.connect(callback)

        def debounced_handler():
            timer.stop()
            timer.start(delay)

        return debounced_handler, timer

# 分类折叠容器类 - 支持基础检查项目的分类和折叠功能
class CategoryCollapsibleContainer(QWidget):
    """分类折叠容器 - 支持将基础检查项目按类别分组并支持折叠/展开"""

    def __init__(self, apps, max_cols=None, cards_per_page=None, parent_widget=None):
        super().__init__()
        self.apps = apps
        self.max_cols = max_cols or AdaptiveConstants.COLS_MAXIMIZED
        self.cards_per_page = cards_per_page or self.max_cols
        self.parent_widget = parent_widget

        # 分类定义
        self.categories = {
            "安全防护设备检查": {
                "items": [
                    "安全帽", 
                    "安全警示牌", 
                    "车轮挡块", 
                    "检查灭火器",  # 移出工具类，归为安全设备
                    "耐磨手套", 
                    "护目镜", 
                    "绝缘手套"    # 个人防护装备
                ],
                "expanded": True
            },
            "检测工具与仪器": {
                "items": [
                    "绝缘测试仪", 
                    "万用表", 
                    "CAN分析仪自检",  # 专业检测设备
                    "网线自检"      # 网络检测工具
                ],
                "expanded": True
            },
            "常规工具检查": {
                "items": [
                    "工具箱自检", 
                    "车内外防护套件"  # 车辆防护工具
                ],
                "expanded": True
            },
            "车辆基础检查": {
                "items": [
                    "车辆外观", 
                    "制动液、冷却液", 
                    "车辆胎压", 
                    "蓄电池电压", 
                    "车辆上电"       # 保持原分类不变
                ],
                "expanded": True
            },
            "电子系统检查": {  # 更准确描述该类别
                "items": [
                    "计算平台", 
                    "检查设备状态"   # 原"传感器检查"优化
                ],
                "expanded": True
            }
        }

        # 智能卡片容器实例和标题栏引用
        self.smart_containers = {}
        self.title_bars = {}

        self._setup_ui()
        self._create_category_sections()

    def _setup_ui(self):
        """设置UI"""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(10)

    def _create_category_sections(self):
        """创建分类区域"""
        for category_name, category_data in self.categories.items():
            # 获取该分类的应用
            category_apps = self._get_apps_for_category(category_name)
            if not category_apps:
                continue

            # 创建分类容器
            category_widget = self._create_single_category_widget(category_name, category_apps)
            self.main_layout.addWidget(category_widget)

    def _get_apps_for_category(self, category_name):
        """获取指定分类的应用列表"""
        category_items = self.categories[category_name]["items"]
        category_apps = []

        for app in self.apps:
            app_name = app.get("name", "").strip()
            if app_name in category_items:
                category_apps.append(app)

        return category_apps

    def _create_single_category_widget(self, category_name, apps):
        """创建单个分类的组件"""
        category_widget = QWidget()
        category_layout = QVBoxLayout(category_widget)
        category_layout.setContentsMargins(0, 0, 0, 0)
        category_layout.setSpacing(5)

        # 创建标题栏
        title_bar = self._create_category_title_bar(category_name)
        self.title_bars[category_name] = title_bar  # 保存引用
        category_layout.addWidget(title_bar)

        # 创建内容容器（智能卡片容器）
        content_container = SmartCardContainer(apps, self.max_cols, self.cards_per_page, self.parent_widget)
        if self.parent_widget and hasattr(self.parent_widget, 'adaptive_manager'):
            content_container.adaptive_manager = self.parent_widget.adaptive_manager

        self.smart_containers[category_name] = content_container
        category_layout.addWidget(content_container)

        return category_widget

    def _create_category_title_bar(self, category_name):
        """创建分类标题栏"""
        title_bar = QWidget()
        title_bar.setStyleSheet("""
            QWidget {
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                padding: 8px;
            }
            QWidget:hover {
                background-color: rgba(255, 255, 255, 0.15);
            }
        """)

        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(10, 5, 10, 5)

        # 展开/收起图标
        icon_label = QLabel("▼" if self.categories[category_name]["expanded"] else "►")
        icon_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                background-color: transparent;
            }
        """)
        icon_label.setObjectName(f"icon_{category_name}")

        # 分类名称
        name_label = QLabel(category_name)
        name_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                font-family: Microsoft YaHei;
                background-color: transparent;
            }
        """)

        title_layout.addWidget(icon_label)
        title_layout.addWidget(name_label)
        title_layout.addStretch()

        # 设置点击事件
        title_bar.mousePressEvent = lambda event: self._toggle_category(category_name)
        title_bar.setCursor(Qt.PointingHandCursor)

        return title_bar

    def _toggle_category(self, category_name):
        """切换分类的展开/收起状态"""
        try:
            current_state = self.categories[category_name]["expanded"]
            new_state = not current_state
            self.categories[category_name]["expanded"] = new_state

            # 更新图标 - 使用保存的引用而不是findChild
            if category_name in self.title_bars:
                title_bar = self.title_bars[category_name]
                icon_label = title_bar.findChild(QLabel, f"icon_{category_name}")
                if icon_label:
                    icon_label.setText("▼" if new_state else "►")

            # 显示/隐藏内容
            if category_name in self.smart_containers:
                container = self.smart_containers[category_name]
                if container:
                    if new_state:
                        container.show()
                    else:
                        container.hide()
        except RuntimeError as e:
            print(f"切换分类状态时出错: {e}")
        except Exception as e:
            print(f"切换分类状态时出现未知错误: {e}")

    def get_all_smart_containers(self):
        """获取所有智能卡片容器 - 用于外部访问"""
        return list(self.smart_containers.values())

    def find_smart_container_by_app(self, app_name):
        """根据应用名称查找对应的智能卡片容器"""
        for category_name, items in self.categories.items():
            if app_name.strip() in items["items"]:
                return self.smart_containers.get(category_name)
        return None

# 智能卡片容器类 - 支持多行网格和单行滑动两种模式
class SmartCardContainer(QWidget):
    """智能卡片容器 - 支持多行网格显示和单行滑动浏览两种模式的动态切换"""

    def __init__(self, apps, max_cols=None, cards_per_page=None, parent_widget=None):
        super().__init__()
        self.apps = apps

        # 设置默认列数
        if max_cols is None:
            max_cols = AdaptiveConstants.COLS_MAXIMIZED  # 使用常量默认值
        self.max_cols = max_cols

        # 设置默认每页卡片数
        if cards_per_page is None:
            cards_per_page = max_cols
        self.cards_per_page = cards_per_page

        self.parent_widget = parent_widget
        self.adaptive_manager = None  # 将在创建时设置

        # 显示模式：'grid' 或 'sliding'
        self.display_mode = 'grid'

        # 滑动模式相关属性
        self.current_page = 0
        self.total_pages = (len(apps) + cards_per_page - 1) // cards_per_page if apps else 0
        self.selected_app = None  # 当前选中的应用

        self._setup_ui()
        self._show_grid_mode()

    def update_adaptive_layout(self):
        """更新自适应布局"""
        if self.adaptive_manager:
            new_max_cols = self.adaptive_manager.get_max_cols()
            if new_max_cols != self.max_cols:
                self.max_cols = new_max_cols
                self.cards_per_page = new_max_cols
                self.total_pages = (len(self.apps) + self.cards_per_page - 1) // self.cards_per_page if self.apps else 0

                # 重新显示当前模式
                if self.display_mode == 'grid':
                    self._show_grid_mode()
                else:
                    self._update_sliding_display()

    def _setup_ui(self):
        """设置UI"""
        self.main_layout = QVBoxLayout(self)
        # 调整智能卡片容器的边距：左、顶部、右、底部
        self.main_layout.setContentsMargins(0, 0, 0, 0)  # 保持无边距，让分类容器控制间距
        self.main_layout.setSpacing(0)

        # 创建卡片显示容器
        self.cards_container = QWidget()

        # 添加到主布局
        self.main_layout.addWidget(self.cards_container)

    def _show_grid_mode(self):
        """显示多行网格模式"""
        self.display_mode = 'grid'

        # 清空容器
        self._clear_container()

        # 创建网格布局
        grid_layout = QGridLayout(self.cards_container)
        grid_layout.setSpacing(0)
        grid_layout.setContentsMargins(0, 0, 0, 0)
        grid_layout.setAlignment(Qt.AlignLeft)

        # 添加所有卡片
        row, col = 0, 0
        for app in self.apps:
            app_widget = AppFrame(app, None, self.parent_widget)
            # 连接点击事件，切换到滑动模式
            app_widget.card_clicked = lambda app_data=app: self._on_card_clicked(app_data)
            grid_layout.addWidget(app_widget, row, col)

            col += 1
            if col >= self.max_cols:
                col = 0
                row += 1

    def _show_sliding_mode(self):
        """显示单行滑动模式"""
        self.display_mode = 'sliding'

        # 清空容器
        self._clear_container()

        # 创建水平布局，使用与网格模式相同的间距和边距设置
        sliding_layout = QHBoxLayout(self.cards_container)
        sliding_layout.setContentsMargins(0, 0, 0, 0)  # 与网格模式保持一致
        sliding_layout.setSpacing(0)  # 与网格模式保持一致
        sliding_layout.setAlignment(Qt.AlignLeft)  # 与网格模式保持一致

        # 更新显示
        self._update_sliding_display()

        # 移除额外的样式设置，保持与网格模式一致的外观
        self.cards_container.setStyleSheet("")

    def _update_sliding_display(self):
        """更新滑动模式的显示"""
        if self.display_mode != 'sliding':
            return

        # 清空现有卡片
        layout = self.cards_container.layout()
        while layout.count():
            child = layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # 计算当前页的卡片范围
        start_idx = self.current_page * self.cards_per_page
        end_idx = min(start_idx + self.cards_per_page, len(self.apps))

        # 添加当前页的卡片
        for i in range(start_idx, end_idx):
            app = self.apps[i]
            app_widget = AppFrame(app, None, self.parent_widget)
            # 连接点击事件（保持与网格模式一致的交互）
            app_widget.card_clicked = lambda app_data=app: self._on_card_clicked(app_data)
            layout.addWidget(app_widget)

        # 添加弹性空间
        layout.addStretch()

    def _clear_container(self):
        """清空卡片容器"""
        if self.cards_container.layout():
            while self.cards_container.layout().count():
                child = self.cards_container.layout().takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
            # 删除布局
            QWidget().setLayout(self.cards_container.layout())

    def _on_card_clicked(self, app_data):
        """处理卡片点击事件"""
        self.selected_app = app_data

        # 找到被点击卡片在哪一页
        for i, app in enumerate(self.apps):
            if app.get('name') == app_data.get('name'):
                self.current_page = i // self.cards_per_page
                break

        # 切换到滑动模式
        self._switch_to_sliding_mode()

        # 触发父窗口的表格显示
        if self.parent_widget and hasattr(self.parent_widget, 'show_basic_check_table'):
            self.parent_widget.show_basic_check_table(app_data)

    def _switch_to_sliding_mode(self):
        """切换到滑动模式"""
        self._show_sliding_mode()

    def wheelEvent(self, event):
        """处理鼠标滚轮事件"""
        if self.display_mode == 'sliding' and self.total_pages > 1:
            # 获取滚轮滚动方向
            delta = event.angleDelta().y()

            if delta > 0:  # 向上滚动，显示上一页
                self.previous_page()
            elif delta < 0:  # 向下滚动，显示下一页
                self.next_page()

            event.accept()  # 阻止事件传播
        else:
            super().wheelEvent(event)

    def next_page(self):
        """下一页"""
        if self.current_page < self.total_pages - 1:
            self.current_page += 1
            self._update_sliding_display()

    def previous_page(self):
        """上一页"""
        if self.current_page > 0:
            self.current_page -= 1
            self._update_sliding_display()


# 布局工厂类
class LayoutFactory:
    """布局工厂类 - 批量创建重复的布局结构"""

    @staticmethod
    def create_grid_with_apps(apps, max_cols=None, parent_widget=None):
        """创建应用网格布局"""
        # 如果没有指定列数，从自适应管理器获取
        if max_cols is None and parent_widget and hasattr(parent_widget, 'adaptive_manager'):
            max_cols = parent_widget.adaptive_manager.get_max_cols()
        elif max_cols is None:
            max_cols = AdaptiveConstants.COLS_MAXIMIZED  # 使用常量默认值

        grid_container = QWidget()
        grid_layout = UIUtils.create_grid_layout(
            spacing=0,
            margins=(0, 0, 0, 0),
            alignment=Qt.AlignLeft
        )

        row, col = 0, 0
        for app in apps:
            app_widget = AppFrame(app, None, parent_widget)
            # 设置自适应管理器
            if parent_widget and hasattr(parent_widget, 'adaptive_manager'):
                app_widget.adaptive_manager = parent_widget.adaptive_manager
                app_widget.update_adaptive_size()
            grid_layout.addWidget(app_widget, row, col)

            col += 1
            if col >= max_cols:
                col = 0
                row += 1

        grid_container.setLayout(grid_layout)
        return grid_container

    @staticmethod
    def create_smart_cards(apps, max_cols=None, cards_per_page=None, parent_widget=None):
        """创建智能卡片容器 - 支持多行网格和单行滑动两种模式"""
        # 如果没有指定列数，从自适应管理器获取
        if max_cols is None and parent_widget and hasattr(parent_widget, 'adaptive_manager'):
            max_cols = parent_widget.adaptive_manager.get_max_cols()
        elif max_cols is None:
            max_cols = AdaptiveConstants.COLS_MAXIMIZED  # 使用常量默认值

        # 卡片数量与列数保持一致
        if cards_per_page is None:
            cards_per_page = max_cols

        smart_container = SmartCardContainer(apps, max_cols, cards_per_page, parent_widget)
        # 设置自适应管理器
        if parent_widget and hasattr(parent_widget, 'adaptive_manager'):
            smart_container.adaptive_manager = parent_widget.adaptive_manager

        return smart_container

    @staticmethod
    def create_collapsible_cards(apps, max_cols=None, cards_per_page=None, parent_widget=None):
        """创建分类折叠卡片容器 - 专门用于基础检查界面"""
        # 如果没有指定列数，从自适应管理器获取
        if max_cols is None and parent_widget and hasattr(parent_widget, 'adaptive_manager'):
            max_cols = parent_widget.adaptive_manager.get_max_cols()
        elif max_cols is None:
            max_cols = AdaptiveConstants.COLS_MAXIMIZED  # 使用常量默认值

        # 卡片数量与列数保持一致
        if cards_per_page is None:
            cards_per_page = max_cols

        collapsible_container = CategoryCollapsibleContainer(apps, max_cols, cards_per_page, parent_widget)

        return collapsible_container

    @staticmethod
    def create_category_frame(category_name, apps, max_cols=None, parent_widget=None):
        """创建分类框架"""
        # 如果没有指定列数，从自适应管理器获取
        if max_cols is None and parent_widget and hasattr(parent_widget, 'adaptive_manager'):
            max_cols = parent_widget.adaptive_manager.get_max_cols()
        elif max_cols is None:
            max_cols = AdaptiveConstants.COLS_MAXIMIZED  # 使用常量默认值

        # 创建分类容器
        category_frame = UIUtils.create_frame_with_style(f"""
            QFrame {{
                border: none;
                border-radius: 8px;
                background-color: rgba(255, 255, 255, 0.1);
                padding: 10px;
                margin-bottom: 15px;
            }}
        """)

        category_layout = QVBoxLayout(category_frame)
        category_layout.setContentsMargins(10, 0, 10, 10)
        category_layout.setSpacing(8)

        # 移除标题 - 根据用户要求
        # if category_name:
        #     title_style = f"""
        #         font-size: 18px;
        #         font-family: Microsoft YaHei;
        #         font-weight: bold;
        #         color: white;
        #         text-align: center;
        #     """
        #     title_label = UIUtils.create_styled_label(
        #         category_name,
        #         style_config=title_style,
        #         alignment=Qt.AlignCenter
        #     )
        #     category_layout.addWidget(title_label)

        # 添加应用网格
        if apps:
            grid_container = LayoutFactory.create_grid_with_apps(apps, max_cols, parent_widget)
            category_layout.addWidget(grid_container)

        return category_frame

    @staticmethod
    def create_title_bar_buttons(parent):
        """创建标题栏按钮组"""
        button_configs = [
            {
                'text': '◐',
                'size': (40, 35),
                'font_size': '18px',
                'callback': parent.toggle_window_size,
                'style_type': 'normal'
            },
            {
                'text': '—',
                'size': (40, 35),
                'font_size': '18px',
                'callback': parent.showMinimized,
                'style_type': 'normal'
            },
            {
                'text': '×',
                'size': (40, 35),
                'font_size': '22px',
                'callback': parent.close,
                'style_type': 'close'
            }
        ]

        buttons = []
        for config in button_configs:
            button = UIUtils.create_styled_button(
                config['text'],
                size=config['size']
            )

            if config['style_type'] == 'close':
                button.setStyleSheet("""
                    QPushButton {
                        color: white;
                        background-color: transparent;
                        border: none;
                        font-size: 22px;
                        font-weight: bold;
                        border-radius: 8px;
                    }
                    QPushButton:hover {
                        background-color: #ff4757;
                        border: 1px solid #ff3742;
                    }
                    QPushButton:pressed {
                        background-color: #ff3742;
                    }
                """)
            else:
                button.setStyleSheet(f"""
                    QPushButton {{
                        color: white;
                        background-color: transparent;
                        border: none;
                        font-weight: bold;
                        border-radius: 8px;
                        font-size: {config['font_size']};
                    }}
                    QPushButton:hover {{
                        background-color: rgba(255, 255, 255, 0.2);
                        border: 1px solid rgba(255, 255, 255, 0.3);
                    }}
                    QPushButton:pressed {{
                        background-color: rgba(255, 255, 255, 0.3);
                    }}
                """)

            button.clicked.connect(config['callback'])
            buttons.append(button)

        return buttons

    @staticmethod
    def create_file_grid(files, max_cols=None, parent_widget=None):
        """创建文件网格布局"""
        # 如果没有指定列数，从自适应管理器获取
        if max_cols is None and parent_widget and hasattr(parent_widget, 'adaptive_manager'):
            max_cols = parent_widget.adaptive_manager.get_max_cols()
        elif max_cols is None:
            max_cols = AdaptiveConstants.COLS_MAXIMIZED  # 使用常量默认值

        grid_container = QWidget()
        grid_layout = UIUtils.create_grid_layout(
            spacing=20,
            margins=(10, 10, 10, 10),
            alignment=Qt.AlignLeft
        )

        row, col = 0, 0
        for file_path in sorted(files):
            file_widget = FileExplorerFrame(file_path, parent_widget)
            # 设置自适应管理器
            if parent_widget and hasattr(parent_widget, 'adaptive_manager'):
                file_widget.adaptive_manager = parent_widget.adaptive_manager
                file_widget.update_adaptive_size()
            grid_layout.addWidget(file_widget, row, col)

            col += 1
            if col >= max_cols:
                col = 0
                row += 1

        grid_container.setLayout(grid_layout)
        return grid_container

# 决策映射类
class DecisionMapper:
    """决策映射类 - 用字典映射替代冗长的if-elif-else链"""

    @staticmethod
    def get_page_handler_map(main_window):
        """获取页面处理器映射"""
        return {
            "首页": main_window.show_home_page,
            "状态监控系统": main_window.show_vehicle_status,
            "故障诊断系统": main_window.show_fault_query_page,
            "流程生成助手": main_window.show_chat_robot_page,
            "社区": main_window.show_community_page,
            "日志管理": main_window.show_log_management_page,
            "主题设置": main_window.show_settings_page
        }

    @staticmethod
    def get_file_extension_handlers():
        """获取文件扩展名处理器映射"""
        return {
            '.txt': 'document',
            '.pdf': 'document',
            '.docx': 'document',
            '.doc': 'document',
            '.md': 'document',
            '.py': 'python_script',
            '.exe': 'executable',
            '.bat': 'batch_script',
            '.sh': 'shell_script'
        }

    @staticmethod
    def get_message_type_handlers(parent):
        """获取消息类型处理器映射"""
        return {
            "info": lambda title, message: QMessageBox.information(parent, title, message),
            "warning": lambda title, message: QMessageBox.warning(parent, title, message),
            "error": lambda title, message: QMessageBox.critical(parent, title, message),
            "question": lambda title, message: QMessageBox.question(parent, title, message)
        }

    @staticmethod
    def is_special_category(category_name):
        """检查是否为特殊分类（不显示在主分类视图中）"""
        special_categories = {
            "状态监控系统", "首页", "故障诊断系统",
            "流程生成助手", "社区", "日志管理", "主题设置"
        }
        return category_name in special_categories

    @staticmethod
    def should_show_help_button(category_name):
        """检查是否应该显示帮助按钮"""
        help_button_categories = {
            "装配", "调试", "测试",
            "标定", "便捷工具", "信息查询"
        }
        return category_name in help_button_categories

# 验证工具类
class ValidationUtils:
    """验证工具类 - 提取公共判断逻辑"""

    @staticmethod
    def is_valid_path(path):
        """验证路径是否有效"""
        if not path:
            return False
        return os.path.exists(path) or path.lower().startswith(('http://', 'https://'))

    @staticmethod
    def is_valid_app_config(app_data):
        """验证应用配置是否有效"""
        if not isinstance(app_data, dict):
            return False

        required_fields = ['name', 'path']
        return all(field in app_data and app_data[field] for field in required_fields)

    @staticmethod
    def is_document_file(file_path):
        """检查是否为文档文件"""
        document_extensions = {'.txt', '.pdf', '.docx', '.doc', '.md'}
        ext = os.path.splitext(file_path)[1].lower()
        return ext in document_extensions

    @staticmethod
    def is_executable_file(file_path):
        """检查是否为可执行文件"""
        executable_extensions = {'.exe', '.bat', '.sh', '.py'}
        ext = os.path.splitext(file_path)[1].lower()
        return ext in executable_extensions

    @staticmethod
    def is_image_file(file_path):
        """检查是否为图像文件"""
        image_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.ico'}
        ext = os.path.splitext(file_path)[1].lower()
        return ext in image_extensions

# 线程管理器
class ThreadManager:
    """线程管理器 - 处理耗时操作，避免UI阻塞"""

    def __init__(self):
        self.worker_threads = []

    def run_in_background(self, task_func, callback=None, error_callback=None):
        """在后台线程中运行任务"""
        from PyQt5.QtCore import QThread, pyqtSignal

        class WorkerThread(QThread):
            finished_signal = pyqtSignal(object)
            error_signal = pyqtSignal(str)

            def __init__(self, task_func):
                super().__init__()
                self.task_func = task_func
                self.result = None

            def run(self):
                try:
                    self.result = self.task_func()
                    self.finished_signal.emit(self.result)
                except Exception as e:
                    self.error_signal.emit(str(e))

        worker = WorkerThread(task_func)

        if callback:
            worker.finished_signal.connect(callback)
        if error_callback:
            worker.error_signal.connect(error_callback)

        # 清理完成的线程
        worker.finished.connect(lambda: self._cleanup_thread(worker))

        self.worker_threads.append(worker)
        worker.start()
        return worker

    def _cleanup_thread(self, thread):
        """清理完成的线程"""
        if thread in self.worker_threads:
            self.worker_threads.remove(thread)
            thread.deleteLater()

    def wait_for_all(self, timeout=5000):
        """等待所有线程完成"""
        for thread in self.worker_threads[:]:  # 创建副本以避免修改列表时的问题
            if thread.isRunning():
                thread.wait(timeout)

    def terminate_all(self):
        """终止所有线程"""
        for thread in self.worker_threads[:]:
            if thread.isRunning():
                thread.terminate()
                thread.wait(1000)  # 等待1秒
            thread.deleteLater()
        self.worker_threads.clear()

# 异步操作工具类
class AsyncUtils:
    """异步操作工具类 - 提供常用的异步操作"""

    @staticmethod
    def load_file_async(file_path, callback, error_callback=None):
        """异步加载文件"""
        def load_task():
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()

        thread_manager = ThreadManager()
        return thread_manager.run_in_background(load_task, callback, error_callback)

    @staticmethod
    def save_file_async(file_path, content, callback=None, error_callback=None):
        """异步保存文件"""
        def save_task():
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True

        thread_manager = ThreadManager()
        return thread_manager.run_in_background(save_task, callback, error_callback)

    @staticmethod
    def process_large_data_async(data, process_func, callback, error_callback=None):
        """异步处理大量数据"""
        def process_task():
            return process_func(data)

        thread_manager = ThreadManager()
        return thread_manager.run_in_background(process_task, callback, error_callback)

# 高效数据处理工具类
class PerformanceUtils:
    """性能优化工具类 - 提供高效的数据处理方法"""

    @staticmethod
    def filter_apps_efficiently(apps_data):
        """高效过滤应用数据"""
        if isinstance(apps_data, dict):
            # 使用字典推导式和内置函数
            return {
                category: [app for app in apps if DataUtils.validate_app_data(app)]
                if isinstance(apps, list) else {
                    subcategory: [app for app in subapps if DataUtils.validate_app_data(app)]
                    for subcategory, subapps in apps.items()
                    if isinstance(subapps, list)
                }
                for category, apps in apps_data.items()
            }
        return apps_data

    @staticmethod
    def batch_create_widgets(widget_class, data_list, parent=None, **kwargs):
        """批量创建组件"""
        # 使用列表推导式批量创建，比循环更高效
        return [widget_class(data, parent, **kwargs) for data in data_list]

    @staticmethod
    def efficient_file_reader(file_path, chunk_size=8192):
        """高效文件读取器 - 逐块读取大文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                while True:
                    chunk = f.read(chunk_size)
                    if not chunk:
                        break
                    yield chunk
        except Exception:
            return

    @staticmethod
    def efficient_file_writer(file_path, content_generator, chunk_size=8192):
        """高效文件写入器 - 逐块写入大文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                for chunk in content_generator:
                    f.write(chunk)
                    if len(chunk) >= chunk_size:
                        f.flush()  # 定期刷新缓冲区
            return True
        except Exception:
            return False

    @staticmethod
    def optimize_list_operations(data_list, operation_func):
        """优化列表操作 - 使用内置函数替代循环"""
        # 使用map函数替代for循环，性能更好
        return list(map(operation_func, data_list))

    @staticmethod
    def efficient_search(items, search_term, key_func=None):
        """高效搜索 - 使用内置filter函数"""
        if key_func:
            return list(filter(lambda item: search_term.lower() in key_func(item).lower(), items))
        else:
            return list(filter(lambda item: search_term.lower() in str(item).lower(), items))

    @staticmethod
    def batch_process_with_progress(items, process_func, progress_callback=None):
        """批量处理数据并显示进度"""
        total = len(items)
        results = []

        for i, item in enumerate(items):
            result = process_func(item)
            results.append(result)

            if progress_callback and i % 10 == 0:  # 每10个项目更新一次进度
                progress = (i + 1) / total * 100
                progress_callback(progress)

        return results

    @staticmethod
    def memory_efficient_sort(items, key_func=None, reverse=False):
        """内存高效的排序 - 原地排序"""
        if key_func:
            items.sort(key=key_func, reverse=reverse)
        else:
            items.sort(reverse=reverse)
        return items

    @staticmethod
    def deduplicate_efficiently(items, key_func=None):
        """高效去重 - 使用集合"""
        if key_func:
            seen = set()
            result = []
            for item in items:
                key = key_func(item)
                if key not in seen:
                    seen.add(key)
                    result.append(item)
            return result
        else:
            # 对于简单类型，直接使用集合
            return list(set(items))

# 缓存管理器
class CacheManager:
    """缓存管理器 - 优化重复计算和数据访问"""

    def __init__(self, max_size=100):
        self.cache = {}
        self.access_order = []
        self.max_size = max_size

    def get(self, key, default=None):
        """获取缓存值"""
        if key in self.cache:
            # 更新访问顺序
            self.access_order.remove(key)
            self.access_order.append(key)
            return self.cache[key]
        return default

    def set(self, key, value):
        """设置缓存值"""
        if key in self.cache:
            # 更新现有值
            self.cache[key] = value
            self.access_order.remove(key)
            self.access_order.append(key)
        else:
            # 添加新值
            if len(self.cache) >= self.max_size:
                # 移除最少使用的项
                oldest_key = self.access_order.pop(0)
                del self.cache[oldest_key]

            self.cache[key] = value
            self.access_order.append(key)

    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.access_order.clear()

    def get_or_compute(self, key, compute_func):
        """获取缓存值或计算新值"""
        value = self.get(key)
        if value is None:
            value = compute_func()
            self.set(key, value)
        return value

# 资源管理器
class ResourceManager:
    """资源管理器 - 优化内存管理和资源释放"""

    def __init__(self):
        self.managed_widgets = []
        self.managed_timers = []
        self.managed_connections = []

    def register_widget(self, widget):
        """注册需要管理的组件"""
        if widget not in self.managed_widgets:
            self.managed_widgets.append(widget)

    def register_timer(self, timer):
        """注册需要管理的定时器"""
        if timer not in self.managed_timers:
            self.managed_timers.append(timer)

    def register_connection(self, connection):
        """注册需要管理的信号连接"""
        if connection not in self.managed_connections:
            self.managed_connections.append(connection)

    def cleanup_widgets(self):
        """清理组件"""
        for widget in self.managed_widgets[:]:  # 创建副本避免修改列表时的问题
            if widget:
                try:
                    widget.hide()
                    widget.setParent(None)
                    widget.deleteLater()
                except:
                    pass
        self.managed_widgets.clear()

    def cleanup_timers(self):
        """清理定时器"""
        for timer in self.managed_timers[:]:
            if timer:
                try:
                    timer.stop()
                    timer.deleteLater()
                except:
                    pass
        self.managed_timers.clear()

    def cleanup_connections(self):
        """清理信号连接"""
        for connection in self.managed_connections[:]:
            if connection:
                try:
                    connection.disconnect()
                except:
                    pass
        self.managed_connections.clear()

    def cleanup_all(self):
        """清理所有资源"""
        self.cleanup_timers()
        self.cleanup_connections()
        self.cleanup_widgets()

# 内存优化工具类
class MemoryUtils:
    """内存优化工具类"""

    @staticmethod
    def safe_delete_widget(widget):
        """安全删除组件"""
        if widget:
            try:
                widget.hide()
                widget.setParent(None)
                widget.deleteLater()
                return True
            except:
                return False
        return False

    @staticmethod
    def clear_layout_safely(layout):
        """安全清理布局"""
        if not layout:
            return

        while layout.count():
            item = layout.takeAt(0)
            if item:
                widget = item.widget()
                if widget:
                    MemoryUtils.safe_delete_widget(widget)
                else:
                    # 处理子布局
                    child_layout = item.layout()
                    if child_layout:
                        MemoryUtils.clear_layout_safely(child_layout)

    @staticmethod
    def optimize_widget_memory(widget):
        """优化组件内存使用"""
        if not widget:
            return

        # 清理不必要的属性
        widget.setAttribute(Qt.WA_DeleteOnClose, True)

        # 如果组件不可见，释放其图形资源
        if not widget.isVisible():
            widget.setAttribute(Qt.WA_DontShowOnScreen, True)

    @staticmethod
    def get_memory_usage():
        """获取内存使用情况（需要psutil库）"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            return {
                'rss': memory_info.rss / 1024 / 1024,  # MB
                'vms': memory_info.vms / 1024 / 1024,  # MB
                'percent': process.memory_percent()
            }
        except ImportError:
            return None

    @staticmethod
    def force_garbage_collection():
        """强制垃圾回收"""
        import gc
        gc.collect()

# 弱引用管理器
class WeakReferenceManager:
    """弱引用管理器 - 避免循环引用导致的内存泄漏"""

    def __init__(self):
        import weakref
        self.weak_refs = {}
        self.weakref = weakref

    def add_reference(self, key, obj):
        """添加弱引用"""
        self.weak_refs[key] = self.weakref.ref(obj)

    def get_reference(self, key):
        """获取弱引用对象"""
        weak_ref = self.weak_refs.get(key)
        if weak_ref:
            obj = weak_ref()
            if obj is None:
                # 对象已被垃圾回收，清理弱引用
                del self.weak_refs[key]
            return obj
        return None

    def cleanup_dead_references(self):
        """清理已死亡的弱引用"""
        dead_keys = []
        for key, weak_ref in self.weak_refs.items():
            if weak_ref() is None:
                dead_keys.append(key)

        for key in dead_keys:
            del self.weak_refs[key]

    def clear_all(self):
        """清理所有弱引用"""
        self.weak_refs.clear()

# 信号优化管理器
class SignalOptimizer:
    """信号优化管理器 - 优化信号与槽连接，添加防抖/节流处理"""

    def __init__(self):
        self.debounce_timers = {}
        self.throttle_timers = {}
        self.signal_connections = {}

    def debounce_signal(self, signal, slot, delay=300, key=None):
        """为信号添加防抖处理"""
        if key is None:
            key = f"{signal}_{slot.__name__}"

        def debounced_slot(*args, **kwargs):
            # 停止之前的定时器
            if key in self.debounce_timers:
                self.debounce_timers[key].stop()

            # 创建新的定时器
            timer = QTimer()
            timer.setSingleShot(True)
            timer.timeout.connect(lambda: slot(*args, **kwargs))
            timer.start(delay)

            self.debounce_timers[key] = timer

        # 连接防抖后的槽函数
        signal.connect(debounced_slot)
        self.signal_connections[key] = (signal, debounced_slot)

        return debounced_slot

    def throttle_signal(self, signal, slot, delay=100, key=None):
        """为信号添加节流处理"""
        if key is None:
            key = f"{signal}_{slot.__name__}"

        last_call_time = [0]  # 使用列表以便在闭包中修改

        def throttled_slot(*args, **kwargs):
            current_time = time.time() * 1000  # 转换为毫秒

            if current_time - last_call_time[0] >= delay:
                last_call_time[0] = current_time
                slot(*args, **kwargs)

        # 连接节流后的槽函数
        signal.connect(throttled_slot)
        self.signal_connections[key] = (signal, throttled_slot)

        return throttled_slot

    def safe_connect(self, signal, slot, connection_type=Qt.AutoConnection):
        """安全连接信号与槽，避免重复连接"""
        key = f"{signal}_{slot.__name__}"

        # 如果已经连接，先断开
        if key in self.signal_connections:
            old_signal, old_slot = self.signal_connections[key]
            try:
                old_signal.disconnect(old_slot)
            except:
                pass

        # 建立新连接
        signal.connect(slot, connection_type)
        self.signal_connections[key] = (signal, slot)

    def disconnect_all(self):
        """断开所有管理的信号连接"""
        for _, (signal, slot) in self.signal_connections.items():  # 使用_表示未使用的key
            try:
                signal.disconnect(slot)
            except:
                pass

        # 停止所有定时器
        for timer in self.debounce_timers.values():
            if timer:
                timer.stop()
                timer.deleteLater()

        for timer in self.throttle_timers.values():
            if timer:
                timer.stop()
                timer.deleteLater()

        # 清理所有记录
        self.signal_connections.clear()
        self.debounce_timers.clear()
        self.throttle_timers.clear()

    def get_connection_count(self):
        """获取当前连接数量"""
        return len(self.signal_connections)

# 高频信号处理器
class HighFrequencySignalHandler:
    """高频信号处理器 - 专门处理高频触发的信号"""

    def __init__(self, update_interval=50):  # 默认50ms更新间隔
        self.update_interval = update_interval
        self.pending_updates = {}
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._process_pending_updates)
        self.update_timer.start(update_interval)

    def schedule_update(self, key, update_func, *args, **kwargs):
        """调度更新操作"""
        self.pending_updates[key] = (update_func, args, kwargs)

    def _process_pending_updates(self):
        """处理待处理的更新"""
        if not self.pending_updates:
            return

        # 处理所有待处理的更新
        for _, (update_func, args, kwargs) in self.pending_updates.items():
            try:
                update_func(*args, **kwargs)
            except Exception:
                pass

        # 清空待处理更新
        self.pending_updates.clear()

    def stop(self):
        """停止处理器"""
        self.update_timer.stop()
        self.pending_updates.clear()

# 信号连接工厂
class SignalConnectionFactory:
    """信号连接工厂 - 创建优化的信号连接"""

    @staticmethod
    def create_scroll_connection(scroll_area, handler, debounce_delay=100):
        """创建优化的滚动信号连接"""
        optimizer = SignalOptimizer()
        return optimizer.debounce_signal(
            scroll_area.verticalScrollBar().valueChanged,
            handler,
            debounce_delay,
            "scroll_handler"
        )

    @staticmethod
    def create_resize_connection(widget, handler, throttle_delay=50):
        """创建优化的调整大小信号连接"""
        optimizer = SignalOptimizer()
        return optimizer.throttle_signal(
            widget.resized if hasattr(widget, 'resized') else None,
            handler,
            throttle_delay,
            "resize_handler"
        )

    @staticmethod
    def create_text_input_connection(line_edit, handler, debounce_delay=300):
        """创建优化的文本输入信号连接"""
        optimizer = SignalOptimizer()
        return optimizer.debounce_signal(
            line_edit.textChanged,
            handler,
            debounce_delay,
            "text_input_handler"
        )

# 丝滑导航处理器
class SmoothNavigationHandler:
    """丝滑导航处理器 - 提供快速响应和平滑滚动"""

    def __init__(self, main_window):
        self.main_window = main_window
        self.last_switch_time = 0
        self.min_switch_interval = 5  # 减少到5ms，超快响应
        self.pending_switch = None
        self.switch_timer = QTimer()
        self.switch_timer.setSingleShot(True)
        self.switch_timer.timeout.connect(self._execute_pending_switch)

        # 内容缓存
        self.content_cache = {}
        self.cache_valid = False

    def request_switch(self, category_name):
        """请求切换分类"""
        # 立即执行切换，不等待
        self._execute_switch(category_name)

    def _execute_pending_switch(self):
        """执行待处理的切换"""
        if self.pending_switch:
            self._execute_switch(self.pending_switch)
            self.pending_switch = None

    def _execute_switch(self, category_name):
        """执行实际的切换操作 - 超快速版本"""
        self.last_switch_time = time.time() * 1000

        # 对于帮助按钮分类，也需要检查是否重复切换
        if category_name in self.main_window.help_button_categories:
            # 检查是否重复切换到同一分类
            if (hasattr(self.main_window, 'last_category') and
                self.main_window.last_category == category_name and
                hasattr(self.main_window, 'current_category') and
                self.main_window.current_category == category_name):
                return

            # 帮助按钮分类的快速切换路径
            self.main_window.last_category = category_name
            self.main_window.current_category = category_name
            self._fast_display_category(category_name)
            return

        # 防止重复切换到同一分类（对非帮助按钮分类）
        if hasattr(self.main_window, 'last_category') and self.main_window.last_category == category_name:
            return

        # 立即更新UI状态
        self.main_window.last_category = category_name

        # 使用决策映射快速切换
        page_handlers = DecisionMapper.get_page_handler_map(self.main_window)

        if category_name in page_handlers:
            # 特殊页面直接切换
            page_handlers[category_name]()
        else:
            # 普通分类页面 - 立即显示内容
            self.main_window.current_category = category_name
            self._fast_display_category(category_name)

    def _fast_display_category(self, category_name):
        """快速显示分类内容 - 修改为单独显示模式"""
        # 所有普通分类都使用单独显示模式
        self.main_window.current_category = category_name
        self.main_window.display_single_category(category_name)



    def invalidate_cache(self):
        """使缓存失效"""
        self.cache_valid = False
        self.content_cache.clear()

# 保持原有的常量以确保向后兼容
HIDDEN_SCROLLBAR_STYLE = StyleConfig.HIDDEN_SCROLLBAR

# ==================== 基础框架类 ====================
class BaseFrameWithTimers(QFrame):
    """带有通用定时器功能的基础框架类"""

    def _setup_common_timers(self):
        """设置通用定时器"""
        self.click_timer = QTimer()
        self.click_timer.setSingleShot(True)
        self.click_timer.timeout.connect(self.handle_single_click)

        self.tooltip_timer = QTimer()
        self.tooltip_timer.setSingleShot(True)
        self.tooltip_timer.timeout.connect(self.show_tooltip)

    def handle_single_click(self):
        """处理单击事件 - 子类可重写"""
        pass

    def show_tooltip(self):
        """显示工具提示 - 子类必须实现"""
        raise NotImplementedError("子类必须实现show_tooltip方法")

# ==================== 可拖拽按钮类 ====================
class DraggableButton(QPushButton):
    """应用程序按钮组件，支持图标和文本显示"""

    def __init__(self, app_data, parent=None):
        super().__init__(parent)
        self.app_data = app_data
        self.hover_animation = None
        self.normal_pos = None
        self.adaptive_manager = None  # 将在主窗口中设置

        self._setup_layout()
        self._setup_icon()
        self._setup_text()
        self._setup_style()

    def _setup_layout(self):
        """设置内部布局"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(10, 10, 10, 10)
        self.layout.setSpacing(0)
        self.layout.addStretch(1)

    def _setup_icon(self):
        """设置图标"""
        self.icon_label = UIUtils.create_styled_label("", alignment=Qt.AlignCenter)
        icon_path = DataUtils.get_app_icon_path(self.app_data)
        icon = QIcon(icon_path)
        pixmap = icon.pixmap(90, 90)
        self.icon_label.setPixmap(pixmap)
        self.layout.addWidget(self.icon_label)
        self.layout.addStretch(1)

    def _setup_text(self):
        """设置文本标签"""
        text_style = f"""
            QLabel {{
                color: #2563eb;
                font-size: 16px;
                font-weight: bold;
                font-family: Microsoft YaHei;
                padding: 5px 0;
                background: transparent;
            }}
        """
        self.text_label = UIUtils.create_styled_label(
            self.app_data['name'],
            style_config=text_style,
            alignment=Qt.AlignCenter
        )
        self.text_label.setWordWrap(True)
        self.layout.addWidget(self.text_label)

    def _setup_style(self):
        """设置按钮样式"""
        self.setFixedSize(240, 180)
        self.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.3 rgba(240, 248, 255, 0.9),
                    stop:0.7 rgba(219, 234, 254, 0.85),
                    stop:1 rgba(191, 219, 254, 0.8));
                border: 2px solid rgba(59, 130, 246, 0.2);
                border-radius: 15px;
                padding: 8px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0),
                    stop:0.3 rgba(239, 246, 255, 0.95),
                    stop:0.7 rgba(219, 234, 254, 0.9),
                    stop:1 rgba(147, 197, 253, 0.85));
                border: 2px solid rgba(59, 130, 246, 0.4);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 246, 255, 0.9),
                    stop:0.3 rgba(219, 234, 254, 0.85),
                    stop:0.7 rgba(191, 219, 254, 0.8),
                    stop:1 rgba(147, 197, 253, 0.75));
                border: 2px solid rgba(59, 130, 246, 0.6);
            }}
        """)

    def update_adaptive_size(self):
        """根据自适应管理器更新尺寸"""
        if self.adaptive_manager:
            width, height = self.adaptive_manager.get_button_size()
            self.setFixedSize(width, height)


# ==================== 应用框架类 ====================
class AppFrame(BaseFrameWithTimers):
    """应用程序框架，包含按钮和工具提示功能"""

    # 类级别变量，用于跟踪全局提示状态
    active_tooltip = None
    tooltip_widget = None

    def __init__(self, app_data, subcategory=None, parent=None):
        super().__init__(parent)
        self.app_data = app_data
        self.subcategory = subcategory
        self.parent_window = parent
        self.tooltip_shown = False

        # 卡片点击回调函数（用于智能卡片容器）
        self.card_clicked = None

        # 自适应管理器
        self.adaptive_manager = None  # 将在主窗口中设置

        # 勾选状态管理
        self.is_completed = False
        self.check_mark_label = None

        # 设置固定大小策略
        self.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)

        self._setup_common_timers()
        self._setup_ui()
        self._setup_tooltip()

    def _setup_ui(self):
        """设置用户界面"""
        # 使用默认尺寸，后续会通过自适应管理器更新
        self.setFixedSize(260, 200)
        self.setStyleSheet("""
            QFrame {
                background-color: transparent;
                border-radius: 15px;
                border: none;
                padding: 0px;
                margin: 0px;
            }
        """)

        self.app_layout = QVBoxLayout(self)
        self.app_layout.setContentsMargins(0, 0, 0, 0)
        self.app_layout.setAlignment(Qt.AlignCenter)

        self.app_btn = DraggableButton(self.app_data, self)
        self.app_btn.clicked.connect(self.on_clicked)
        self.app_btn.installEventFilter(self)
        self.app_btn.setMouseTracking(False)

        self._add_shadow_effect()
        self.app_layout.addWidget(self.app_btn, alignment=Qt.AlignCenter)

        # 创建勾选标记（初始隐藏）
        self._create_check_mark()

        self.installEventFilter(self)
        self.setMouseTracking(True)

    def _create_check_mark(self):
        """创建勾选标记"""
        self.check_mark_label = QLabel("✓", self)
        self.check_mark_label.setStyleSheet("""
            QLabel {
                color: #4CAF50;
                font-size: 48px;
                font-weight: bold;
                background-color: rgba(255, 255, 255, 0.9);
                border-radius: 25px;
                border: 3px solid #4CAF50;
            }
        """)
        self.check_mark_label.setAlignment(Qt.AlignCenter)
        self.check_mark_label.setFixedSize(50, 50)
        self.check_mark_label.hide()  # 初始隐藏

        # 将勾选标记放置在卡片中心
        self._position_check_mark()

    def _position_check_mark(self):
        """定位勾选标记到卡片中心"""
        if self.check_mark_label:
            # 计算中心位置
            x = (self.width() - self.check_mark_label.width()) // 2
            y = (self.height() - self.check_mark_label.height()) // 2
            self.check_mark_label.move(x, y)

    def set_completed_status(self, completed):
        """设置完成状态"""
        self.is_completed = completed
        if self.check_mark_label:
            if completed:
                self.check_mark_label.show()
                self.check_mark_label.raise_()  # 确保在最上层
            else:
                self.check_mark_label.hide()

    def resizeEvent(self, event):
        """重写resize事件，确保勾选标记始终在中心"""
        super().resizeEvent(event)
        self._position_check_mark()

    def _toggle_basic_check_panel(self):
        """切换基础检查面板的显示/隐藏"""
        if not self.parent_window:
            return

        check_item_name = self.app_data.get('name', '').strip()

        # 检查该项目是否已完成
        if (hasattr(self.parent_window, 'completed_check_items') and
            check_item_name in self.parent_window.completed_check_items):
            # 已完成的项目不响应双击
            return

        # 检查是否已经有该项目的面板
        if (hasattr(self.parent_window, 'active_panels') and
            check_item_name in self.parent_window.active_panels):
            # 如果已有面板，隐藏并移除
            panel = self.parent_window.active_panels[check_item_name]
            if panel:
                panel.hide()
                self.parent_window.panels_main_layout.removeWidget(panel)
                panel.deleteLater()
                del self.parent_window.active_panels[check_item_name]

                # 如果没有活跃面板了，隐藏整个面板容器
                if len(self.parent_window.active_panels) == 0:
                    if hasattr(self.parent_window, 'panels_main_container'):
                        self.parent_window.panels_main_container.hide()
        else:
            # 如果没有面板，显示面板
            self.parent_window.show_basic_check_table(self.app_data)

    def update_adaptive_size(self):
        """根据自适应管理器更新尺寸"""
        if self.adaptive_manager:
            width, height = self.adaptive_manager.get_card_size()
            self.setFixedSize(width, height)
            # 同时更新内部按钮的尺寸
            if hasattr(self, 'app_btn'):
                self.app_btn.adaptive_manager = self.adaptive_manager
                self.app_btn.update_adaptive_size()

    def _add_shadow_effect(self):
        """添加阴影效果 - 延迟加载以避免启动卡顿"""
        # 延迟500ms加载阴影效果，避免启动时卡顿
        QTimer.singleShot(100, self._apply_shadow_effect)

    def _apply_shadow_effect(self):
        """实际应用阴影效果"""
        shadow = UIUtils.create_shadow_effect()
        if shadow:
            self.app_btn.setGraphicsEffect(shadow)

    def _setup_tooltip(self):
        """设置工具提示 - 延迟初始化以避免启动卡顿"""
        # 工具提示将在首次需要时才创建
        pass

    def _ensure_tooltip_widget(self):
        """确保工具提示组件已创建"""
        if AppFrame.tooltip_widget is None:
            AppFrame.tooltip_widget = QLabel(None, Qt.ToolTip)
            AppFrame.tooltip_widget.setStyleSheet(f"""
                QLabel {{
                    background-color: #f8f9fa;
                    color: #2c3e50;
                    font-family: SimSun;
                    font-size: 15px;
                    border: 1px solid #e0e0e0;
                    padding: 10px;
                    border-radius: 6px;
                }}
            """)
            AppFrame.tooltip_widget.setWordWrap(True)
            AppFrame.tooltip_widget.setMinimumWidth(300)
            AppFrame.tooltip_widget.setMaximumWidth(450)
            AppFrame.tooltip_widget.hide()
    def eventFilter(self, obj, event):
        """事件过滤器，处理鼠标事件"""
        if event.type() == QEvent.MouseButtonDblClick:
            self.click_timer.stop()
            # 检查是否是基础检查分类的应用
            if (self.parent_window and
                hasattr(self.parent_window, 'current_category') and
                self.parent_window.current_category == "基础检查"):
                # 基础检查项目双击切换面板显示/隐藏
                self._toggle_basic_check_panel()
            else:
                # 其他分类正常启动应用
                self.parent_window.launch_app(self.app_data)
            return True
        elif event.type() == QEvent.Enter:
            if not self.tooltip_timer.isActive() and not self.tooltip_shown:
                self.tooltip_timer.start(300)
            return False
        elif event.type() == QEvent.Leave and obj == self:
            if self.tooltip_shown and AppFrame.active_tooltip == self:
                AppFrame.tooltip_widget.hide()
                AppFrame.active_tooltip = None
                self.tooltip_shown = False
            self.tooltip_timer.stop()
            return False
        return super().eventFilter(obj, event)

    def show_tooltip(self):
        """显示工具提示"""
        if not self.tooltip_shown:
            # 确保工具提示组件已创建
            self._ensure_tooltip_widget()

            if AppFrame.active_tooltip and AppFrame.active_tooltip != self:
                AppFrame.active_tooltip.tooltip_shown = False

            description = DataUtils.get_app_description(self.app_data)
            AppFrame.tooltip_widget.setText(description)
            AppFrame.tooltip_widget.adjustSize()

            global_pos = self.mapToGlobal(QPoint(
                self.width() // 2 - AppFrame.tooltip_widget.width() // 2,
                -AppFrame.tooltip_widget.height() - 5
            ))
            AppFrame.tooltip_widget.move(global_pos)
            AppFrame.tooltip_widget.show()

            self.tooltip_shown = True
            AppFrame.active_tooltip = self

    def on_clicked(self):
        """处理点击事件"""
        self.click_timer.start(QApplication.doubleClickInterval())

    def handle_single_click(self):
        """处理单击事件 - 重写基类方法"""
        # 如果有卡片点击回调（智能卡片容器模式），优先调用回调
        if hasattr(self, 'card_clicked') and self.card_clicked:
            self.card_clicked(self.app_data)
            return

        # 检查是否是基础检查分类的应用
        if (self.parent_window and
            hasattr(self.parent_window, 'current_category') and
            self.parent_window.current_category == "基础检查"):
            # 显示基础检查表格
            self.parent_window.show_basic_check_table(self.app_data)
        else:
            # 其他分类保持原有行为（双击启动）
            pass



# ==================== 文件浏览器框架类 ====================
class FileExplorerFrame(BaseFrameWithTimers):
    """文件浏览器框架，用于显示文件和文件夹"""

    def __init__(self, file_path, parent=None):
        super().__init__(parent)
        self.file_path = file_path
        self.parent_window = parent
        self.tooltip_shown = False
        self.adaptive_manager = None  # 将在主窗口中设置

        self._setup_common_timers()
        self._setup_ui()
        self._setup_file_button()
        self._setup_file_label()
        self._setup_event_filters()

    def _setup_ui(self):
        """设置用户界面"""
        self.setFixedSize(150, 140)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: #ffffff;
                border-radius: 0px;
                border: none;
                padding: 5px 2px;
            }}
            QFrame:hover {{
                background-color: #e8f0f8;
                border: none;
            }}
        """)

        self.file_layout = QVBoxLayout(self)
        self.file_layout.setContentsMargins(0, 0, 0, 0)
        self.file_layout.setSpacing(3)
        self.file_layout.setAlignment(Qt.AlignTop | Qt.AlignCenter)

    def _setup_file_button(self):
        """设置文件按钮"""
        self.file_btn = QPushButton(self)
        self.file_btn.setFixedSize(55, 55)
        self.file_btn.setIconSize(QSize(42, 42))
        self.file_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                border: none;
            }}
            QPushButton:hover {{
                background-color: rgba(0, 0, 0, 0.05);
                border-radius: 5px;
            }}
        """)

        icon_provider = QFileIconProvider()
        file_info = QFileInfo(self.file_path)
        self.file_btn.setIcon(icon_provider.icon(file_info))
        self.file_btn.clicked.connect(self.on_clicked)
        self.file_btn.installEventFilter(self)
        self.file_btn.setMouseTracking(False)
        self.file_layout.addWidget(self.file_btn, alignment=Qt.AlignCenter)

    def _setup_file_label(self):
        """设置文件标签"""
        file_name = os.path.basename(self.file_path)
        self.file_label = QLabel(file_name)
        self.file_label.setAlignment(Qt.AlignCenter)
        self.file_label.setWordWrap(True)
        self.file_label.setMinimumHeight(45)
        self.file_label.setMaximumHeight(55)
        self.file_label.setMinimumWidth(125)
        self.file_label.setStyleSheet(f"""
            QLabel {{
                font-size: 12px;
                font-family: SimSun;
                color: #333333;
                font-weight: normal;
                background-color: transparent;
                padding: 2px 0px;
                line-height: 1.3;
                margin: 0 2px;
            }}
        """)
        self.file_label.setMouseTracking(False)
        self.file_layout.addWidget(self.file_label, alignment=Qt.AlignCenter)

    def _setup_event_filters(self):
        """设置事件过滤器"""
        self.installEventFilter(self)
        self.file_label.installEventFilter(self)
        self.setMouseTracking(True)

    def eventFilter(self, obj, event):
        """事件过滤器，处理鼠标事件"""
        if event.type() == QEvent.MouseButtonDblClick:
            self.click_timer.stop()
            self.on_file_clicked()
            return True
        elif event.type() == QEvent.Enter:
            if not self.tooltip_timer.isActive() and not self.tooltip_shown:
                self.tooltip_timer.start(300)
            return False
        elif event.type() == QEvent.Leave and obj == self:
            if self.tooltip_shown and AppFrame.active_tooltip == self:
                AppFrame.tooltip_widget.hide()
                AppFrame.active_tooltip = None
                self.tooltip_shown = False
            self.tooltip_timer.stop()
            return False
        return super().eventFilter(obj, event)

    def show_tooltip(self):
        """显示工具提示"""
        if not self.tooltip_shown:
            if AppFrame.active_tooltip and AppFrame.active_tooltip != self:
                AppFrame.active_tooltip.tooltip_shown = False

            file_name = os.path.basename(self.file_path)
            description = f"文件名: {file_name}\n路径: {self.file_path}"

            AppFrame.tooltip_widget.setText(description)
            AppFrame.tooltip_widget.adjustSize()

            global_pos = self.mapToGlobal(QPoint(
                self.width() // 2 - AppFrame.tooltip_widget.width() // 2,
                -AppFrame.tooltip_widget.height() - 5
            ))
            AppFrame.tooltip_widget.move(global_pos)
            AppFrame.tooltip_widget.show()

            self.tooltip_shown = True
            AppFrame.active_tooltip = self

    def on_clicked(self):
        """处理点击事件"""
        self.click_timer.start(QApplication.doubleClickInterval())



    def on_file_clicked(self):
        """处理文件点击事件"""
        if os.path.isdir(self.file_path):
            self.parent_window.open_folder(self.file_path)
        else:
            self.open_file(self.file_path)

    def open_file(self, file_path):
        """打开文件"""
        logger = get_logger()
        logger.log_user_action(f"打开文件: {os.path.basename(file_path)}")

        try:
            if ValidationUtils.is_document_file(file_path) or ValidationUtils.is_executable_file(file_path):
                QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))
            else:
                # 尝试用默认程序打开
                QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))
        except Exception as e:
            error_msg = f"无法打开文件：{str(e)}"
            if not ValidationUtils.is_document_file(file_path) and not ValidationUtils.is_executable_file(file_path):
                error_msg = f"不支持的文件类型或无法打开：{str(e)}"
            QMessageBox.warning(self.parent_window, "打开失败", error_msg)

    def update_adaptive_size(self):
        """根据自适应管理器更新尺寸"""
        if self.adaptive_manager:
            width, height = self.adaptive_manager.get_file_size()
            self.setFixedSize(width, height)

# ==================== 主窗口类 ====================
class MainWindow(QMainWindow):
    """主窗口类，包含所有界面组件和功能"""

    def __init__(self):
        super().__init__()

        self._init_window_properties()
        self._init_variables()
        self._load_styles()
        self.init_ui()
        self._preload_components()

    def _init_window_properties(self):
        """初始化窗口属性"""
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowSystemMenuHint)
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        self.setWindowTitle("智测魔方")
        self.setMinimumSize(1400, 900)

        self._set_window_icon()
        self.shadow_margin = 15

    def _set_window_icon(self):
        """设置窗口图标"""
        icon_path = "../image/start.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
            QApplication.instance().setWindowIcon(QIcon(icon_path))
        else:
            png_icon_path = "../image/start.png"
            if os.path.exists(png_icon_path):
                self.setWindowIcon(QIcon(png_icon_path))
                QApplication.instance().setWindowIcon(QIcon(png_icon_path))

    def _init_variables(self):
        """初始化变量"""
        # 窗口拖拽相关
        self.drag_position = None
        self.is_dragging = False
        self.resize_mode = None
        self.resize_start_pos = None
        self.resize_start_geometry = None

        # 配置和数据
        self.config_file = "apps_config.json"
        self.current_category = None
        self.current_subcategory = None
        self.navigation_history = []
        self.current_active_feature = None

        # 布局相关
        self.is_maximized = False
        self.max_cols = DEFAULT_MAX_COLS

        # 界面组件
        self.vehicle_status_widget = None
        self.fault_query_widget = None
        self.settings_page_widget = None
        self.chat_robot_widget = None
        self.community_widget = None

        # 自适应布局管理器
        self.adaptive_manager = AdaptiveLayoutManager(self)

        # 性能优化相关
        self.cached_widgets = {}
        self.last_category = None
        self._programmatic_scroll = False
        self.anchor_positions = {}
        self.last_scroll_update_time = 0

        # 线程管理器
        self.thread_manager = ThreadManager()

        # 缓存管理器
        self.cache_manager = CacheManager(max_size=50)

        # 资源管理器
        self.resource_manager = ResourceManager()

        # 弱引用管理器
        self.weak_ref_manager = WeakReferenceManager()

        # 信号优化管理器
        self.signal_optimizer = SignalOptimizer()

        # 高频信号处理器
        self.high_freq_handler = HighFrequencySignalHandler()

        # 丝滑导航处理器
        self.smooth_nav_handler = SmoothNavigationHandler(self)

        # 在所有管理器初始化完成后加载配置数据
        self.apps_data = self.load_config_optimized()

        # 帮助按钮配置
        self.help_button_categories = ["装配", "调试", "测试", "标定", "便捷工具", "信息查询"]

        # 滚动防抖定时器（保留用于向后兼容，但不再使用）
        self.scroll_debounce_timer = QTimer()
        self.scroll_debounce_timer.setSingleShot(True)
        # 不再连接到处理函数，使用高频信号处理器替代

        # 窗口大小状态 - 只保留最大化和恢复窗口两种状态
        self.restored_window_size = None  # 恢复窗口大小，将在初始化时计算

    def _load_styles(self):
        """加载样式表 - 已禁用外部CSS文件"""
        # 禁用外部CSS文件，避免覆盖我们的分类颜色设置
        # try:
        #     with open('styles.qss', 'r', encoding='utf-8') as f:
        #         self.setStyleSheet(f.read())
        # except Exception:
        #     pass
        pass

    def _preload_components(self):
        """预加载组件 - 使用真正的异步加载，避免卡顿"""
        # 使用线程管理器进行真正的异步预加载，不阻塞主线程
        def preload_task():
            # 在后台线程中准备数据，不创建UI组件
            return {
                'settings_ready': True,
                'vehicle_status_ready': True
            }

        def on_preload_complete(result):
            # 预加载完成后的回调，在主线程中执行
            self.preload_status = result

        # 延迟10秒后开始预加载，确保用户已经开始使用程序
        QTimer.singleShot(10000, lambda: self.thread_manager.run_in_background(
            preload_task, on_preload_complete
        ))
    def _get_or_create_vehicle_status_widget(self):
        """按需创建车辆状态监控页面"""
        if not hasattr(self, 'vehicle_status_widget') or self.vehicle_status_widget is None:
            try:
                self.vehicle_status_widget = VehicleStatusWidget()
            except Exception:
                return None
        return self.vehicle_status_widget

    def _get_or_create_settings_widget(self):
        """按需创建设置页面"""
        if not hasattr(self, 'settings_page_widget') or self.settings_page_widget is None:
            try:
                self.settings_page_widget = SettingsPage(self)
            except Exception:
                return None
        return self.settings_page_widget

    # ==================== 界面初始化 ====================
    def init_ui(self):
        """初始化用户界面"""
        self._create_main_container()
        self._create_title_bar()
        self._create_content_area()
        self._setup_window_size()

    def _create_main_container(self):
        """创建主容器"""
        main_container = QWidget()
        self.setCentralWidget(main_container)

        container_layout = QVBoxLayout(main_container)
        container_layout.setContentsMargins(self.shadow_margin, self.shadow_margin,
                                          self.shadow_margin, self.shadow_margin)

        self.content_widget = QWidget()
        self.content_widget.setObjectName("contentWidget")
        self.content_widget.setStyleSheet(f"""
            QWidget#contentWidget {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1a1a2e, stop:0.3 #16213e, stop:0.7 #0f3460, stop:1 #0e4b99);
                border-radius: 20px;
                border: 2px solid rgba(100, 200, 255, 0.3);
            }}
        """)

        self._add_main_shadow()
        container_layout.addWidget(self.content_widget)

        self.main_vertical_layout = QVBoxLayout(self.content_widget)
        self.main_vertical_layout.setContentsMargins(0, 0, 0, 0)
        self.main_vertical_layout.setSpacing(0)

    def _add_main_shadow(self):
        """添加主窗口阴影效果 - 延迟加载"""
        # 延迟1秒加载主窗口阴影，避免启动时卡顿
        QTimer.singleShot(1000, self._apply_main_shadow)

    def _apply_main_shadow(self):
        """实际应用主窗口阴影效果"""
        shadow = create_shadow_effect(blur_radius=25, color=QColor(0, 0, 0, 80), offset_y=10)
        if shadow:
            self.content_widget.setGraphicsEffect(shadow)

    def _create_title_bar(self):
        """创建标题栏"""
        self.title_bar = QFrame()
        self.title_bar.setObjectName("titleBar")
        self.title_bar.setFixedHeight(65)  # 增加高度
        self.title_bar.setStyleSheet(f"""
            QFrame#titleBar {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(30, 144, 255, 0.9),
                    stop:0.5 rgba(0, 191, 255, 0.8),
                    stop:1 rgba(135, 206, 250, 0.9));
                border-top-left-radius: 18px;
                border-top-right-radius: 18px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            }}
        """)

        title_layout = QHBoxLayout(self.title_bar)
        title_layout.setContentsMargins(20, 0, 15, 0)

        # 添加左侧弹性空间 - 支持小数比例调整
        # 使用整数来模拟小数效果，例如：1.5 = 15, 0.5 = 5
        left_stretch_ratio = 15  # 相当于 1.0
        title_layout.addStretch(left_stretch_ratio)

        # 添加居中的标题和图标
        self._add_centered_title_with_icon(title_layout)

        # 添加右侧弹性空间 - 支持小数比例调整
        right_stretch_ratio = 10  # 相当于 1.0
        title_layout.addStretch(right_stretch_ratio)

        self._create_control_buttons(title_layout)

        self.main_vertical_layout.addWidget(self.title_bar)

    def _add_centered_title_with_icon(self, layout):
        """添加居中的标题和图标"""
        # 创建水平容器来放置标题和图标
        title_container = QWidget()
        title_container_layout = QHBoxLayout(title_container)
        title_container_layout.setContentsMargins(0, 0, 0, 0)
        title_container_layout.setSpacing(10)

        # 添加标题文本
        title_label = QLabel("智测魔方")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-family: Microsoft YaHei;
                font-size: 22px;
                font-weight: bold;
                background: transparent;
                border: none;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        title_container_layout.addWidget(title_label)

        # 添加图标
        icon_label = QLabel()
        icon_path = "../image/start.ico"
        if os.path.exists(icon_path):
            pixmap = QPixmap(icon_path)
            # 调整图标大小
            scaled_pixmap = pixmap.scaled(28, 28, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            icon_label.setPixmap(scaled_pixmap)
        else:
            # 如果ico文件不存在，尝试使用png文件
            png_icon_path = "../image/start.png"
            if os.path.exists(png_icon_path):
                pixmap = QPixmap(png_icon_path)
                scaled_pixmap = pixmap.scaled(28, 28, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon_label.setPixmap(scaled_pixmap)

        icon_label.setAlignment(Qt.AlignCenter)
        title_container_layout.addWidget(icon_label)

        layout.addWidget(title_container)

    def _add_app_icon(self, layout):
        """添加应用图标"""
        app_icon = QLabel()
        try:
            app_icon.setPixmap(QIcon("../image/sensor_icon.png").pixmap(32, 32))
        except:
            app_icon.setText("🔧")
            app_icon.setStyleSheet("font-size: 24px; color: white;")
        layout.addWidget(app_icon)

    def _add_title_label(self, layout):
        """添加标题标签"""
        title_label = QLabel("传感器工具箱")
        title_label.setStyleSheet(f"""
            color: white;
            font-size: 20px;
            font-weight: bold;
            font-family: Comic Sans MS;
            margin-left: 10px;
            background-color: transparent;
        """)
        layout.addWidget(title_label)

    def _create_control_buttons(self, layout):
        """创建窗口控制按钮"""
        buttons = LayoutFactory.create_title_bar_buttons(self)
        self.size_toggle_button = buttons[0]
        self.min_button = buttons[1]
        self.close_button = buttons[2]

        # 添加按钮到布局
        for button in buttons:
            layout.addWidget(button)

    def _create_content_area(self):
        """创建内容区域"""
        content_widget = QWidget()
        content_widget.setObjectName("contentArea")
        main_layout = QHBoxLayout(content_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        self.main_vertical_layout.addWidget(content_widget)

        self._create_category_list(main_layout)
        self._create_scroll_area(main_layout)
        self._create_ai_chat_button()

    def _create_category_list(self, main_layout):
        """创建全新的分类列表 - 使用简单有效的颜色方案"""
        # 创建一个容器来放置分类按钮
        from PyQt5.QtWidgets import QVBoxLayout, QWidget, QPushButton
        from PyQt5.QtCore import Qt

        # 创建侧边栏容器
        sidebar_widget = QWidget()
        sidebar_widget.setMaximumWidth(400)
        sidebar_widget.setMinimumWidth(180)

        # 设置侧边栏背景
        sidebar_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(20, 30, 48, 0.95),
                    stop:0.5 rgba(25, 42, 86, 0.9),
                    stop:1 rgba(30, 58, 138, 0.95));
                border: none;
                border-bottom-left-radius: 18px;
            }
        """)

        # 创建垂直布局
        sidebar_layout = QVBoxLayout(sidebar_widget)
        sidebar_layout.setContentsMargins(2, 15, 2, 15)
        sidebar_layout.setSpacing(3)

        # 定义分类和颜色
        categories_with_colors = [
            # 更浅蓝色系 - 核心检测功能
            ("基础检查", "#124182"),
            ("装配", "#124182"),
            ("调试", "#124182"),
            ("测试", "#124182"),
            ("标定", "#124182"),
            # 更浅绿色系 - 扩展功能工具
            ("项目进度监控", "#1E40AF"),
            ("状态监控系统", "#1E40AF"),
            ("故障诊断系统", "#1E40AF"),
            ("流程生成助手", "#1E40AF"),
            # 更浅橙色系 - 管理设置功能
            ("便捷工具", "#427AA1"),
            ("信息查询", "#427AA1"),
            ("社区", "#427AA1"),
            ("日志管理", "#427AA1"),
            ("主题设置", "#427AA1"),
            ("首页", "#427AA1"),
        ]

        # 存储按钮引用
        self.category_buttons = {}
        self.current_selected_button = None

        # 创建分类按钮
        for category_name, color in categories_with_colors:
            btn = QPushButton(category_name)
            btn.setMinimumHeight(60)
            btn.setMaximumHeight(60)

            # 设置按钮样式 - 直接在样式表中设置背景颜色
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 12px;
                    padding: 15px;
                    font-size: 18px;
                    font-weight: 500;
                    text-align: left;
                    border-left: 4px solid transparent;
                }}
                QPushButton:hover {{
                    border-left: 5px solid rgba(255, 255, 255, 0.7);
                    background-color: {color};
                }}
                QPushButton:pressed {{
                    background-color: {color};
                    border-left: 6px solid #ffffff;
                }}
            """)

            # 连接点击事件
            btn.clicked.connect(lambda checked, name=category_name: self._on_category_button_clicked(name))

            # 添加到布局
            sidebar_layout.addWidget(btn)
            self.category_buttons[category_name] = btn

        # 添加弹性空间
        sidebar_layout.addStretch()

        # 添加到主布局
        main_layout.addWidget(sidebar_widget)

        # 创建一个虚拟的category_list来保持兼容性
        class MockItem:
            def __init__(self, text):
                self._text = text
            def text(self):
                return self._text

        class MockSignal:
            def connect(self, func):
                pass
            def disconnect(self):
                pass

        class MockCategoryList:
            def __init__(self, categories):
                self.categories = categories
                self.current_index = 0
                self.currentItemChanged = MockSignal()

            def count(self):
                return len(self.categories)

            def item(self, i):
                if 0 <= i < len(self.categories):
                    return MockItem(self.categories[i][0])
                return None

            def currentItem(self):
                if hasattr(self, '_parent') and hasattr(self._parent, 'current_category'):
                    return MockItem(self._parent.current_category)
                return MockItem("基础检查")

            def setCurrentRow(self, row):
                if hasattr(self, '_parent'):
                    self._parent._select_category_by_index(row)

            def blockSignals(self, block):
                pass

            def setCurrentItem(self, item):
                pass

        self.category_list = MockCategoryList(categories_with_colors)
        self.category_list._parent = self  # 设置父对象引用

        # 默认选中基础检查
        self.current_category = "基础检查"
        self._update_button_selection("基础检查")

    def _on_category_button_clicked(self, category_name):
        """处理分类按钮点击"""
        # 更新选中状态
        self._update_button_selection(category_name)

        # 设置当前分类
        self.current_category = category_name

        # 调用原有的分类选择逻辑
        # 创建一个模拟的item对象
        class MockSelectedItem:
            def __init__(self, text):
                self._text = text
            def text(self):
                return self._text

        mock_item = MockSelectedItem(category_name)
        self.on_category_selected(mock_item)

    def _update_button_selection(self, selected_category):
        """更新按钮选中状态"""
        for category_name, btn in self.category_buttons.items():
            if category_name == selected_category:
                # 选中状态 - 添加白色左边框
                color = self._get_category_color(category_name)
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 12px;
                        padding: 15px;
                        font-size: 16px;
                        font-weight: bold;
                        text-align: left;
                        border-left: 6px solid #ffffff;
                    }}
                    QPushButton:hover {{
                        border-left: 6px solid #ffffff;
                        background-color: {color};
                    }}
                """)
                self.current_selected_button = btn
            else:
                # 未选中状态
                color = self._get_category_color(category_name)
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 12px;
                        padding: 15px;
                        font-size: 16px;
                        font-weight: 500;
                        text-align: left;
                        border-left: 4px solid transparent;
                    }}
                    QPushButton:hover {{
                        border-left: 5px solid rgba(255, 255, 255, 0.7);
                        background-color: {color};
                    }}
                """)

    def _get_category_color(self, category_name):
        """获取分类对应的颜色"""
        if category_name in ["基础检查", "装配", "调试", "测试", "标定"]:
            return "#124182"  # 更浅蓝色
        elif category_name in ["项目进度监控", "状态监控系统", "故障诊断系统", "流程生成助手", ]:
            return "#1E40AF"  # 更浅绿色
        else:
            return "#427AA1"  # 更浅橙色

    def _get_category_hint_text(self, category_name):
        """获取分类对应的提示文字"""
        hint_texts = {
            "基础检查": "请选择需要检查的项目",
            "装配": "请选择需要装配的项目",
            "调试": "请选择需要调试的项目",
            "标定": "请选择需要标定的项目",
            "测试": "请选择需要测试的项目",
            "便捷工具": "请选择需要使用的工具",
            "信息查询": "请选择需要查询的内容"
        }
        return hint_texts.get(category_name, f"请选择：{category_name}")

    def _select_category_by_index(self, index):
        """根据索引选择分类"""
        categories = ["基础检查", "装配", "调试", "测试", "标定", "项目进度监控", "状态监控系统", "故障诊断系统",  "流程生成助手", "便捷工具", "信息查询","社区", "日志管理", "主题设置", "首页"]
        if 0 <= index < len(categories):
            self._on_category_button_clicked(categories[index])

    def _set_category_font(self):
        """设置分类列表字体 - 新系统中不需要"""
        pass

    def _set_category_style(self):
        """设置分类列表基础样式 - 新系统中不需要"""
        pass

    def _populate_categories(self):
        """填充分类列表 - 新系统中已在_create_category_list中完成"""
        # 新的按钮系统已经在_create_category_list中创建了所有分类
        # 这个方法保留为空以保持兼容性
        pass

    def _apply_basic_style_only(self):
        """只应用基础样式，不影响颜色"""
        base_style = f"""
            QListWidget {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(20, 30, 48, 0.95),
                    stop:0.5 rgba(25, 42, 86, 0.9),
                    stop:1 rgba(30, 58, 138, 0.95));
                border: none;
                border-bottom-left-radius: 18px;
                color: #ffffff;
                outline: none;
                padding: 5px 0 15px 0;
            }}
            QListWidget::item {{
                padding: 15px 15px;
                margin: 2px 8px;
                border-radius: 12px;
                outline: none;
                min-height: 45px;
                border-left: 4px solid transparent;
                font-weight: 500;
                color: white;
                font-size: 16px;
                /* 不设置背景颜色，保持项目自己的颜色 */
            }}
            QListWidget::item:selected {{
                color: white;
                font-weight: bold;
                border-left: 6px solid #ffffff;
            }}
            QListWidget::item:hover {{
                border-left: 5px solid rgba(255, 255, 255, 0.7);
            }}
            QListWidget::item:focus {{
                outline: none;
                border: none;
            }}
            {HIDDEN_SCROLLBAR_STYLE}
        """
        self.category_list.setStyleSheet(base_style)

    def _apply_new_category_color_system(self):
        """应用全新的分类颜色系统 - 从一开始就显示分类颜色"""
        from PyQt5.QtGui import QBrush, QColor

        # 定义三大类颜色方案 - 使用更柔和的浅色系
        category_color_groups = {
            # 第一类：核心检测功能 - 更浅蓝色系
            "core_inspection": {
                "categories": ["基础检查", "装配", "调试", "测试", "标定"],
                "base_color": QColor(153, 204, 255, 255),  # 更浅蓝色，完全不透明
                "selected_color": QColor(102, 179, 255, 255),  # 选中时稍深的浅蓝色
                "hover_color": QColor(179, 217, 255, 255),  # 悬停时的颜色
                "border_color": "#124182"
            },
            # 第二类：扩展功能工具 - 更浅绿色系
            "extended_tools": {
                "categories": ["项目进度监控",  "状态监控系统", "故障诊断系统","流程生成助手",],
                "base_color": QColor(153, 230, 153, 255),  # 更浅绿色，完全不透明
                "selected_color": QColor(102, 217, 102, 255),  # 选中时稍深的浅绿色
                "hover_color": QColor(179, 242, 179, 255),  # 悬停时的颜色
                "border_color": "#1E40AF"
            },
            # 第三类：管理设置功能 - 更浅橙色系
            "management": {
                "categories": ["便捷工具", "信息查询","社区", "日志管理", "主题设置", "首页"],
                "base_color": QColor(255, 204, 153, 255),  # 更浅橙色，完全不透明
                "selected_color": QColor(255, 179, 102, 255),  # 选中时稍深的浅橙色
                "hover_color": QColor(255, 217, 179, 255),  # 悬停时的颜色
                "border_color": "#427AA1"
            }
        }

        # 创建颜色映射表
        color_mapping = {}
        for group_name, group_data in category_color_groups.items():
            for category in group_data["categories"]:
                color_mapping[category] = {
                    "base_color": group_data["base_color"],
                    "selected_color": group_data["selected_color"],
                    "hover_color": group_data["hover_color"],
                    "border_color": group_data["border_color"],
                    "group": group_name
                }

        # 设置基础样式表 - 保持原有样式
        base_style = f"""
            QListWidget {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(20, 30, 48, 0.95),
                    stop:0.5 rgba(25, 42, 86, 0.9),
                    stop:1 rgba(30, 58, 138, 0.95));
                border: none;
                border-bottom-left-radius: 18px;
                color: #ffffff;
                outline: none;
                padding: 5px 0 15px 0;
            }}
            QListWidget::item {{
                padding: 15px 15px;
                margin: 2px 8px;
                border-radius: 12px;
                outline: none;
                min-height: 45px;
                border-left: 4px solid transparent;
                font-weight: 500;
                color: white;
                font-size: 16px;
            }}
            QListWidget::item:selected {{
                color: white;
                font-weight: bold;
                border-left: 6px solid #ffffff;
            }}
            QListWidget::item:hover {{
                border-left: 5px solid rgba(255, 255, 255, 0.7);
            }}
            QListWidget::item:focus {{
                outline: none;
                border: none;
            }}
            {HIDDEN_SCROLLBAR_STYLE}
        """

        # 先设置基础样式
        self.category_list.setStyleSheet(base_style)

        # 强制设置每个分类项的背景颜色 - 在样式设置后立即执行
        for i in range(self.category_list.count()):
            item = self.category_list.item(i)
            category_name = item.text()

            if category_name in color_mapping:
                color_info = color_mapping[category_name]
                base_color = color_info["base_color"]

                # 强制设置背景颜色
                item.setBackground(QBrush(base_color))

                # 确保文本颜色为白色
                item.setForeground(QBrush(QColor(255, 255, 255)))

                # 使用定时器延迟再次设置，确保颜色不被覆盖
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(100, lambda item=item, color=base_color: item.setBackground(QBrush(color)))

        # 存储颜色映射供其他方法使用
        self.category_color_mapping = color_mapping

        # 强制刷新显示
        self.category_list.update()
        self.category_list.repaint()

    def _enhanced_category_selection_handler(self, current, previous):
        """增强的分类选择处理器 - 处理颜色高亮和选择状态"""
        if not current:
            return

        # 获取当前选中项的颜色信息
        current_color_info = current.data(Qt.UserRole)

        if current_color_info:
            # 为选中项应用高亮效果
            selected_color = current_color_info.get("selected_color")
            if selected_color:
                from PyQt5.QtGui import QBrush
                # 临时高亮选中项（会被CSS的selected样式覆盖，但提供额外的视觉反馈）
                current.setBackground(QBrush(selected_color))

        # 恢复之前选中项的普通颜色
        if previous:
            previous_color_info = previous.data(Qt.UserRole)
            if previous_color_info:
                base_color = previous_color_info.get("base_color")
                if base_color:
                    from PyQt5.QtGui import QBrush
                    previous.setBackground(QBrush(base_color))

    def _setup_category_selection_events(self):
        """设置分类选择事件处理"""
        # 连接增强的选择处理器
        self.category_list.currentItemChanged.connect(self._enhanced_category_selection_handler)
        # 保持原有的选择处理逻辑
        self.category_list.currentItemChanged.connect(self.on_category_selected)



    def _create_scroll_area(self, main_layout):
        """创建滚动区域"""
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setFrameShape(QFrame.NoFrame)
        self.scroll_area.setObjectName("scrollArea")

        self._set_scroll_area_style()
        self._create_scroll_content()
        self._setup_scroll_events()

        main_layout.addWidget(self.scroll_area)

    def _set_scroll_area_style(self):
        """设置滚动区域样式"""
        self.scroll_area.setStyleSheet(f"""
            QScrollArea {{
                background: transparent;
                border: none;
            }}
            {HIDDEN_SCROLLBAR_STYLE}
        """)

    def _create_scroll_content(self):
        """创建滚动内容"""
        self.scroll_content = QWidget()
        self.scroll_content.setObjectName("scrollContent")
        self.scroll_content.setStyleSheet("""
            QWidget#scrollContent {
                background: transparent;
            }
        """)
        self.scroll_content.setContentsMargins(0, 0, 0, 0)

        self.scroll_layout = QVBoxLayout(self.scroll_content)
        self.scroll_layout.setContentsMargins(5, 0, 15, 10)  # 将顶部边距设为0，为提示文字腾出空间
        self.scroll_layout.setSpacing(25)  # 恢复原来的间距

        self.scroll_area.setWidget(self.scroll_content)
        self.scroll_area.setAutoFillBackground(False)
        self.scroll_content.setAutoFillBackground(False)

    def _setup_scroll_events(self):
        """设置滚动事件"""
        # 使用信号优化管理器添加防抖处理
        self.signal_optimizer.debounce_signal(
            self.scroll_area.verticalScrollBar().valueChanged,
            self.on_scroll_changed,
            delay=100,
            key="scroll_changed"
        )
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

    def _create_ai_chat_button(self):
        """创建AI对话按钮"""
        self.ai_chat_btn = QPushButton()
        self.ai_chat_btn.setIcon(QIcon("../image/bz.png"))
        self.ai_chat_btn.setIconSize(QSize(48, 48))
        self.ai_chat_btn.setFixedSize(56, 56)
        self.ai_chat_btn.setToolTip("点击获取帮助")
        self.ai_chat_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #1C64F2;
                border-radius: 28px;
                padding: 4px;
            }}
            QPushButton:hover {{
                background-color: #165CE3;
            }}
        """)
        self.ai_chat_btn.clicked.connect(self.toggle_ai_chat)
        self.ai_chat_btn.hide()
    def _setup_window_size(self):
        """设置窗口大小和初始状态"""

        self._setup_ai_chat_container()

        # 计算恢复窗口尺寸（屏幕宽度的2/3，高度保持1080不变）
        from PyQt5.QtWidgets import QDesktopWidget
        screen = QDesktopWidget().screenGeometry()
        restored_width = int(screen.width() * 2 / 3)
        restored_height = 1080
        self.restored_window_size = (restored_width, restored_height)

        # 设置窗口初始状态为恢复窗口（非最大化）
        width, height = self.restored_window_size
        # 居中显示恢复窗口
        x = (screen.width() - width) // 2
        y = (screen.height() - height) // 2
        self.setGeometry(x, y, width, height)
        self.is_maximized = False
        self.max_cols = self.adaptive_manager.get_max_cols()  # 使用自适应管理器获取列数
        self.setMouseTracking(True)

    def _setup_ai_chat_container(self):
        """设置AI对话容器"""
        self.ai_chat_container = QWidget(self)
        self.ai_chat_container.setFixedSize(70, 70)
        self.ai_chat_layout = QVBoxLayout(self.ai_chat_container)
        self.ai_chat_layout.setContentsMargins(0, 0, 0, 0)
        self.ai_chat_layout.addWidget(self.ai_chat_btn, alignment=Qt.AlignCenter)

        self.ai_chat_visible = False
        self.ai_chat_iframe = None

        self.ai_chat_container.setParent(self)
        self.ai_chat_container.move(self.width() - 100, self.height() - 80)

    # ==================== 窗口事件处理 ====================
    def toggle_window_size(self):
        """切换窗口大小 - 只在最大化和恢复窗口之间切换"""
        if self.is_maximized:
            # 切换到恢复窗口
            width, height = self.restored_window_size
            # 居中显示恢复窗口
            from PyQt5.QtWidgets import QDesktopWidget
            screen = QDesktopWidget().screenGeometry()
            x = (screen.width() - width) // 2
            y = (screen.height() - height) // 2
            self.setGeometry(x, y, width, height)
            self.is_maximized = False
        else:
            # 切换到最大化窗口
            self.showMaximized()
            self.is_maximized = True

        # 更新自适应布局
        self.adaptive_manager.update_window_state(self.is_maximized)

        # 重新布局当前内容
        if hasattr(self, 'current_category') and self.current_category:
            # 如果当前分类不是特殊页面，使用单独显示模式
            if not DecisionMapper.is_special_category(self.current_category):
                self.display_single_category(self.current_category)
            else:
                self.display_all_categories()

    def mousePressEvent(self, event):
        """鼠标按下事件 - 用于窗口拖拽"""
        if event.button() == Qt.LeftButton:
            # 检查是否点击在标题栏区域
            if self.title_bar.geometry().contains(event.pos()):
                self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
                self.is_dragging = True
                event.accept()
            # 禁用窗口调整大小功能
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 用于窗口拖拽和调整大小"""
        if event.buttons() == Qt.LeftButton:
            if self.is_dragging and self.drag_position:
                # 窗口拖拽 - 现在允许拖拽移动
                self.move(event.globalPos() - self.drag_position)
                event.accept()
            elif self.resize_mode and self.resize_start_pos:
                # 窗口调整大小 - 禁用调整大小功能
                event.accept()
        else:
            # 禁用调整大小光标
            self.setCursor(Qt.ArrowCursor)
        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self.is_dragging = False
            self.drag_position = None
            self.resize_mode = None
            self.resize_start_pos = None
            self.resize_start_geometry = None
            self.setCursor(Qt.ArrowCursor)
        super().mouseReleaseEvent(event)

    def _get_resize_mode(self, pos):
        """获取调整大小模式"""
        margin = 10  # 边缘检测范围
        rect = self.rect()

        if pos.x() <= margin and pos.y() <= margin:
            return "top_left"
        elif pos.x() >= rect.width() - margin and pos.y() <= margin:
            return "top_right"
        elif pos.x() <= margin and pos.y() >= rect.height() - margin:
            return "bottom_left"
        elif pos.x() >= rect.width() - margin and pos.y() >= rect.height() - margin:
            return "bottom_right"
        elif pos.x() <= margin:
            return "left"
        elif pos.x() >= rect.width() - margin:
            return "right"
        elif pos.y() <= margin:
            return "top"
        elif pos.y() >= rect.height() - margin:
            return "bottom"
        return None

    def _set_resize_cursor(self, mode):
        """设置调整大小光标"""
        cursor_map = {
            "top_left": Qt.SizeFDiagCursor,
            "top_right": Qt.SizeBDiagCursor,
            "bottom_left": Qt.SizeBDiagCursor,
            "bottom_right": Qt.SizeFDiagCursor,
            "left": Qt.SizeHorCursor,
            "right": Qt.SizeHorCursor,
            "top": Qt.SizeVerCursor,
            "bottom": Qt.SizeVerCursor
        }
        self.setCursor(cursor_map.get(mode, Qt.ArrowCursor))

    def _handle_resize(self, global_pos):
        """处理窗口调整大小"""
        if not self.resize_start_pos or not self.resize_start_geometry:
            return

        delta = global_pos - self.resize_start_pos
        new_geometry = self.resize_start_geometry

        if "left" in self.resize_mode:
            new_geometry.setLeft(new_geometry.left() + delta.x())
        if "right" in self.resize_mode:
            new_geometry.setRight(new_geometry.right() + delta.x())
        if "top" in self.resize_mode:
            new_geometry.setTop(new_geometry.top() + delta.y())
        if "bottom" in self.resize_mode:
            new_geometry.setBottom(new_geometry.bottom() + delta.y())

        # 确保最小尺寸
        if new_geometry.width() < self.minimumWidth():
            if "left" in self.resize_mode:
                new_geometry.setLeft(new_geometry.right() - self.minimumWidth())
            else:
                new_geometry.setRight(new_geometry.left() + self.minimumWidth())

        if new_geometry.height() < self.minimumHeight():
            if "top" in self.resize_mode:
                new_geometry.setTop(new_geometry.bottom() - self.minimumHeight())
            else:
                new_geometry.setBottom(new_geometry.top() + self.minimumHeight())

        self.setGeometry(new_geometry)

    def resizeEvent(self, event):
        """窗口大小改变事件 - 更新AI聊天按钮位置"""
        super().resizeEvent(event)
        if hasattr(self, 'ai_chat_container'):
            # 更新AI聊天按钮位置到右下角
            self.ai_chat_container.move(
                self.width() - 100 - self.shadow_margin,
                self.height() - 80 - self.shadow_margin
            )

    def mouseDoubleClickEvent(self, event):
        """鼠标双击事件 - 禁用双击标题栏切换最大化"""
        if event.button() == Qt.LeftButton:
            if self.title_bar.geometry().contains(event.pos()):
                # 禁用双击切换最大化功能
                event.accept()
        super().mouseDoubleClickEvent(event)

    # ==================== AI对话功能 ====================
    def toggle_ai_chat(self):
        """切换AI对话框显示状态"""
        try:
            # 检查当前分类是否应该显示帮助按钮
            current_category = ""
            if self.category_list.currentItem():
                current_category = self.category_list.currentItem().text()
                
            if current_category not in self.help_button_categories:
                return  # 如果当前分类不在允许列表中，不显示帮助对话框
                
            # 使用ChatDialog类，显示在窗口右下角
            if not self.ai_chat_visible:
                # 创建对话框
                self.chat_dialog = ChatDialog(self)

                # 更新对话框的自适应尺寸
                self.chat_dialog.update_adaptive_size()

                # 设置对话框位置在右下角
                dialog_x = self.width() - self.chat_dialog.width() - 50
                dialog_y = self.height() - self.chat_dialog.height() - 20
                self.chat_dialog.move(dialog_x, dialog_y)

                self.chat_dialog.show()
                self.ai_chat_visible = True
            else:
                # 关闭对话框
                if hasattr(self, 'chat_dialog'):
                    self.chat_dialog.close()
                    self.chat_dialog.deleteLater()
                    self.ai_chat_visible = False
        except Exception as e:
            QMessageBox.warning(self, "打开AI对话框失败", f"无法打开AI对话: {str(e)}")

    # ==================== 分类显示功能 ====================
    def display_single_category(self, category_name):
        """显示单个分类的应用 - 新的独立显示模式"""
        # 清理当前布局
        self._clear_scroll_layout()

        self.scroll_content.setStyleSheet(f"""
            QWidget#scrollContent {{
                background-color: #3a7bd9;
                border-bottom-right-radius: 18px;
            }}
        """)

        # 为指定界面添加顶部提示文字
        if category_name in ["基础检查", "装配", "调试", "标定", "测试", "便捷工具", "信息查询"]:
            hint_text = self._get_category_hint_text(category_name)
            hint_label = QLabel(hint_text)
            hint_label.setStyleSheet("""
                QLabel {
                    font-size: 22px;
                    font-family: Microsoft YaHei;
                    color: rgba(255, 255, 255, 0.8);
                    padding: 0px;
                    margin: 0px 0px 0px 0px;
                    background-color: transparent;
                    min-height: 30px;
                    max-height: 0px;
                }
            """)
            hint_label.setAlignment(Qt.AlignCenter)
            self.scroll_layout.addWidget(hint_label)

        # 只显示当前选中的分类
        self._create_single_category_container(category_name)
        self.scroll_layout.addStretch()

    def _create_single_category_container(self, category_name):
        """创建单个分类容器"""
        # 创建分类容器
        category_container = QFrame()
        category_container.setObjectName(f"category_container_{category_name}")
        category_container.setStyleSheet("""
            QFrame {
                background-color: transparent;
                border-radius: 15px;
                padding: 0px;
                margin: 0px 10px 15px 10px;
                border: none;
            }
        """)

        category_layout = QVBoxLayout(category_container)
        category_layout.setContentsMargins(20, 0, 20, 15)
        category_layout.setSpacing(15)

        # 移除分类标题 - 根据用户要求
        # category_title = QLabel(category_name)
        # category_title.setStyleSheet(f"""
        #     QLabel {{
        #         font-size: 28px;
        #         font-family: SimHei;
        #         font-weight: bold;
        #         color: white;
        #         padding-bottom: 15px;
        #         border-bottom: 3px solid rgba(255, 255, 255, 0.4);
        #         margin-bottom: 20px;
        #         text-align: center;
        #     }}
        # """)
        # category_title.setAlignment(Qt.AlignCenter)
        # category_layout.addWidget(category_title)

        # 添加分类内容
        category_data = self.apps_data.get(category_name, {})
        if isinstance(category_data, dict) and category_data:
            self._display_category_with_subcategories(category_data, category_layout)
        else:
            self._display_category_grid(category_data, category_layout, category_name)

        self.scroll_layout.addWidget(category_container)

    def display_all_categories(self):
        """显示所有分类的应用，每个分类作为一个区域 - 优化版本"""
        # 检查是否为特殊页面
        if self.category_list.currentItem() and self.category_list.currentItem().text() in ["传感器状态监控系统", "首页", "故障诊断系统", "流程生成助手",  "社区", "日志管理", "主题设置"]:
            pass

        # 清理当前布局
        self._clear_scroll_layout()

        self.scroll_content.setStyleSheet(f"""
            QWidget#scrollContent {{
                background-color: #3a7bd9;
                border-bottom-right-radius: 18px;
            }}
        """)

        categories_to_display = []
        for i in range(self.category_list.count()):
            category_name = self.category_list.item(i).text()
            if not DecisionMapper.is_special_category(category_name):
                categories_to_display.append(category_name)

        # 保持原始顺序，不进行重新排序，确保锚点位置与导航栏顺序一致

        for category_name in categories_to_display:
            # 添加分类锚点
            anchor = QWidget()
            anchor.setObjectName(f"anchor_{category_name}")
            anchor.setFixedHeight(1)
            anchor.setStyleSheet("background-color: transparent;")
            self.scroll_layout.addWidget(anchor)
            
            # 创建并添加普通分类容器
            category_container = QFrame()
            category_container.setObjectName(f"category_container_{category_name}")
            if category_name in ["调试", "测试"]:  # 为主要区域头部添加特殊样式
                category_container.setStyleSheet("""
                    QFrame {
                        background-color: transparent;
                        border-radius: 15px;
                        padding: 15px;
                        margin: 0px 10px;
                        border: none;
                    }
                """)
                category_layout = QVBoxLayout(category_container)
                category_layout.setContentsMargins(20, 15, 20, 15)
                category_layout.setSpacing(10)
            else:
                category_container.setStyleSheet("""
                    QFrame {
                        background-color: transparent;
                        border-radius: 15px;
                        padding: 20px;
                        margin: 0px 10px;
                        border: none;
                    }
                """)
                category_layout = QVBoxLayout(category_container)
                category_layout.setContentsMargins(20, 15, 20, 15)
                category_layout.setSpacing(15)
            
            # 移除分类标题 - 根据用户要求
            # category_title = QLabel(category_name)
            # if category_name in ["调试", "测试"]:  # 为主要区域头部标题添加特殊样式
            #     category_title.setStyleSheet(f"""
            #         QLabel {{
            #             font-size: 24px;
            #             font-family: SimHei;
            #             font-weight: bold;
            #             color: white;
            #             padding-bottom: 5px;
            #             border-bottom: 2px solid rgba(255, 255, 255, 0.3);
            #             margin-bottom: 3px;
            #             text-align: center;
            #         }}
            #     """)
            #     category_title.setAlignment(Qt.AlignCenter)
            # else:
            #     category_title.setStyleSheet(f"""
            #         QLabel {{
            #             font-size: 24px;
            #             font-family: SimHei;
            #             font-weight: bold;
            #             color: white;
            #             padding-bottom: 8px;
            #             border-bottom: 2px solid rgba(255, 255, 255, 0.3);
            #             margin-bottom: 5px;
            #             text-align: center;
            #         }}
            #     """)
            #     category_title.setAlignment(Qt.AlignCenter)
            # category_layout.addWidget(category_title)
            
            category_data = self.apps_data.get(category_name, {})
            if isinstance(category_data, dict) and category_data:
                self._display_category_with_subcategories(category_data, category_layout)
            else:
                self._display_category_grid(category_data, category_layout, category_name)
            
            self.scroll_layout.addWidget(category_container)

        self.scroll_layout.addStretch()

    def display_all_categories_fast(self):
        """快速显示所有分类 - 修复版本"""
        # 只有在缓存无效或内容为空时才重新创建
        if (not hasattr(self, '_categories_cache_valid') or
            not self._categories_cache_valid or
            self.scroll_layout.count() == 0):
            self._display_categories_immediate()

    def _display_categories_immediate(self):
        """立即显示分类内容，不使用异步加载"""
        # 根据当前分类使用适当的显示模式
        if hasattr(self, 'current_category') and self.current_category:
            if not DecisionMapper.is_special_category(self.current_category):
                self.display_single_category(self.current_category)
            else:
                self.display_all_categories()
        else:
            self.display_all_categories()
        self._categories_cache_valid = True

    def _display_basic_structure(self):
        """立即显示基本结构"""
        # 清理当前布局
        self._clear_scroll_layout()

        # 设置背景样式
        self.scroll_content.setStyleSheet(f"""
            QWidget#scrollContent {{
                background-color: #3a7bd9;
                border-bottom-right-radius: 18px;
            }}
        """)

        # 获取要显示的分类
        categories_to_display = []
        for i in range(self.category_list.count()):
            category_name = self.category_list.item(i).text()
            if not DecisionMapper.is_special_category(category_name):
                categories_to_display.append(category_name)

        # 立即创建锚点和占位符
        for category_name in categories_to_display:
            # 添加分类锚点
            anchor = QWidget()
            anchor.setObjectName(f"anchor_{category_name}")
            anchor.setFixedHeight(1)
            anchor.setStyleSheet("background-color: transparent;")
            self.scroll_layout.addWidget(anchor)

            # 创建占位符容器
            placeholder = QFrame()
            placeholder.setObjectName(f"placeholder_{category_name}")
            placeholder.setFixedHeight(50)
            placeholder.setStyleSheet("background-color: transparent;")
            self.scroll_layout.addWidget(placeholder)

        self.scroll_layout.addStretch()

    def _load_category_content(self):
        """加载分类具体内容"""
        # 根据当前分类使用适当的显示模式
        if hasattr(self, 'current_category') and self.current_category:
            if not DecisionMapper.is_special_category(self.current_category):
                self.display_single_category(self.current_category)
            else:
                self.display_all_categories()
        else:
            self.display_all_categories()
        self._categories_cache_valid = True
    

    def _render_category_content_to_layout(self, category_data, target_layout):
        """渲染分类内容到指定布局"""
        if isinstance(category_data, dict) and category_data: # Potentially has subcategories
            for subcategory, apps in category_data.items():
                subcategory_frame = QFrame()
                subcategory_frame.setStyleSheet(f"""
                    QFrame {{
                        border: none;
                        border-radius: 8px;
                        background-color: rgba(255, 255, 255, 0.1);
                        padding: 0px 10px 10px 10px;
                        margin-bottom: 15px;
                    }}
                """)
                subcategory_layout = QVBoxLayout(subcategory_frame)
                subcategory_layout.setContentsMargins(10, 10, 10, 10)
                subcategory_layout.setSpacing(8)
                
                title_container = QWidget()
                title_layout = QHBoxLayout(title_container)
                title_layout.setContentsMargins(0,0,0,2)
                subcategory_title_label = QLabel(subcategory)
                subcategory_title_label.setStyleSheet(f"""
                    font-size: 18px;
                    font-family: Microsoft YaHei;
                    font-weight: bold;
                    color: white;
                    text-align: center;
                """)
                subcategory_title_label.setAlignment(Qt.AlignCenter)
                title_layout.addWidget(subcategory_title_label, 1)  # 设置stretch因子1，使标题居中
                subcategory_layout.addWidget(title_container)

                if apps:
                    grid_container = QWidget()
                    grid_layout = QGridLayout(grid_container)
                    grid_layout.setSpacing(0)  # 水平间距
                    grid_layout.setVerticalSpacing(0)  # 减少垂直间距
                    grid_layout.setContentsMargins(0, 0, 0, 0)
                    grid_layout.setAlignment(Qt.AlignLeft)  # 左对齐
                    row, col = 0, 0
                    max_cols = DEFAULT_MAX_COLS

                    for app in apps:
                        app_widget = AppFrame(app, subcategory, self)
                        grid_layout.addWidget(app_widget, row, col)
                        col += 1
                        if col >= max_cols:
                            col = 0
                            row += 1
                    subcategory_layout.addWidget(grid_container)
                
                target_layout.addWidget(subcategory_frame)

        elif isinstance(category_data, list) and category_data: # Simple list of apps (grid display)
            grid_container = QWidget()
            grid_layout = QGridLayout(grid_container)
            grid_layout.setSpacing(0)  # 水平间距
            grid_layout.setVerticalSpacing(0)  # 减少垂直间距
            grid_layout.setContentsMargins(0, 0, 0, 0)
            grid_layout.setAlignment(Qt.AlignLeft)  # 左对齐
            row, col = 0, 0
            max_cols = DEFAULT_MAX_COLS

            for app in category_data:
                app_widget = AppFrame(app, None, self) # No subcategory
                grid_layout.addWidget(app_widget, row, col)
                col += 1
                if col >= max_cols:
                    col = 0
                    row += 1
            target_layout.addWidget(grid_container)
    
    def on_category_selected(self, current, previous=None):
        """处理分类选择事件 - 优化版本，快速响应"""
        # previous参数保留用于向后兼容，但当前实现中未使用
        _ = previous  # 标记为已使用以避免警告
        if not current:
            return

        # 记录导航点击时间，用于智能滚动同步
        self.last_navigation_time = time.time() * 1000

        current_item_text = current.text().strip()

        # 立即更新UI状态，提供即时反馈
        if DecisionMapper.should_show_help_button(current_item_text):
            self.ai_chat_btn.show()
        else:
            self.ai_chat_btn.hide()

        # 记录用户操作（超简化版本，仅对非帮助按钮分类）
        if current_item_text not in self.help_button_categories:
            try:
                logger = get_logger()
                if self.current_active_feature and self.current_active_feature != current_item_text:
                    logger.log_feature_end(self.current_active_feature)
                logger.log_feature_start(current_item_text)
                logger.log_user_action(f"点击导航栏 '{current_item_text}'")
                self.current_active_feature = current_item_text
            except:
                pass  # 日志记录失败不应影响导航

        # 使用丝滑导航处理器处理切换
        self.smooth_nav_handler.request_switch(current_item_text)

    def _switch_to_category(self, category_name):
        """切换到指定分类 - 修改为单独显示模式"""
        # 隐藏任何显示的工具提示
        self._hide_all_tooltips()

        page_handlers = DecisionMapper.get_page_handler_map(self)

        if category_name in page_handlers:
            # 特殊页面（如首页、状态监控等）
            page_handlers[category_name]()
        else:
            # 普通工具箱分类，使用单独显示模式
            self.current_category = category_name
            self.display_single_category(category_name)


    def scroll_to_category(self, category_name):
        """直接跳转到指定分类的位置 - 超快速版本，立即响应"""
        # 停止任何正在进行的滚动动画
        if hasattr(self, 'smooth_scroll_animation') and self.smooth_scroll_animation.state() == QPropertyAnimation.Running:
            self.smooth_scroll_animation.stop()

        # 强制更新布局，确保所有组件都已正确定位
        self.scroll_content.updateGeometry()
        QApplication.processEvents()

        # 查找目标锚点
        target_widget = self.scroll_content.findChild(QWidget, f"anchor_{category_name}")

        if target_widget:
            scrollbar = self.scroll_area.verticalScrollBar()

            target_pos = target_widget.mapTo(self.scroll_content, QPoint(0,0)).y()

            # 如果位置是0或负数，尝试通过索引计算
            if target_pos <= 0:
                target_pos = self._calculate_anchor_position_by_index(category_name)

            # 立即跳转到目标位置
            if target_pos >= 0:
                scrollbar.setValue(target_pos)
            else:
                # 如果计算的位置仍然无效，不要跳转到顶部，保持当前位置
                pass
        else:
            # 如果找不到锚点，尝试通过索引计算位置
            target_pos = self._calculate_anchor_position_by_index(category_name)
            if target_pos >= 0:
                self.scroll_area.verticalScrollBar().setValue(target_pos)

    def smooth_scroll_to_category(self, category_name):
        """丝滑滚动到指定分类的位置"""
        # 强制更新布局，确保锚点位置正确
        self.scroll_content.updateGeometry()
        QApplication.processEvents()

        # 查找目标锚点
        target_widget = self.scroll_content.findChild(QWidget, f"anchor_{category_name}")
        if target_widget:
            scrollbar = self.scroll_area.verticalScrollBar()

            # 确保组件可见并更新几何信息
            target_widget.show()
            target_widget.updateGeometry()
            QApplication.processEvents()

            # 重新计算位置
            target_pos = target_widget.mapTo(self.scroll_content, QPoint(0,0)).y()

            # 如果位置仍然是0，尝试通过布局索引计算
            if target_pos <= 0:
                target_pos = self._calculate_anchor_position_by_index(category_name)

            if target_pos >= 0:
                # 停止之前的动画
                if hasattr(self, 'smooth_scroll_animation') and self.smooth_scroll_animation.state() == QPropertyAnimation.Running:
                    self.smooth_scroll_animation.stop()

                # 创建丝滑滚动动画
                self.smooth_scroll_animation = QPropertyAnimation(scrollbar, b"value")
                self.smooth_scroll_animation.setDuration(400)  # 400ms动画时间，丝滑体验
                self.smooth_scroll_animation.setStartValue(scrollbar.value())
                self.smooth_scroll_animation.setEndValue(target_pos)
                self.smooth_scroll_animation.setEasingCurve(QEasingCurve.OutCubic)  # 使用OutCubic缓动，更丝滑

                # 启动动画
                self.smooth_scroll_animation.start()
        else:
            # 如果找不到锚点，平滑滚动到顶部
            self._smooth_scroll_to_top()

    def _smooth_scroll_to_top(self):
        """平滑滚动到顶部"""
        scrollbar = self.scroll_area.verticalScrollBar()
        if scrollbar.value() > 0:
            if hasattr(self, 'smooth_scroll_animation') and self.smooth_scroll_animation.state() == QPropertyAnimation.Running:
                self.smooth_scroll_animation.stop()

            self.smooth_scroll_animation = QPropertyAnimation(scrollbar, b"value")
            self.smooth_scroll_animation.setDuration(300)
            self.smooth_scroll_animation.setStartValue(scrollbar.value())
            self.smooth_scroll_animation.setEndValue(0)
            self.smooth_scroll_animation.setEasingCurve(QEasingCurve.OutCubic)
            self.smooth_scroll_animation.start()

    def _calculate_anchor_position_by_index(self, category_name):
        """通过布局索引计算锚点位置"""
        try:
            # 获取分类显示顺序
            categories_to_display = []
            for i in range(self.category_list.count()):
                cat_name = self.category_list.item(i).text()
                if not DecisionMapper.is_special_category(cat_name):
                    categories_to_display.append(cat_name)

            if category_name not in categories_to_display:
                return 0

            target_index = categories_to_display.index(category_name)

            # 累计计算位置
            total_height = 0
            for i in range(target_index):
                # 每个分类大约的高度（锚点 + 标题 + 内容 + 间距）
                category_height = self._estimate_category_height(categories_to_display[i])
                total_height += category_height

            return total_height

        except Exception:
            return 0

    def _estimate_category_height(self, category_name):
        """估算分类的高度"""
        # 基础高度：标题 + 间距
        base_height = 80

        # 根据分类中的应用数量估算内容高度
        if category_name in self.apps_data:
            apps = self.apps_data[category_name]
            if isinstance(apps, list):
                app_count = len(apps)
            elif isinstance(apps, dict):
                app_count = sum(len(subapps) if isinstance(subapps, list) else 0
                              for subapps in apps.values())
            else:
                app_count = 0

            # 每行6个应用，每个应用约120px高度
            rows = (app_count + 5) // 6  # 向上取整
            content_height = rows * 120

            return base_height + content_height

        return base_height

    def on_scroll_changed(self, value=None):
        """滚动条位置改变时的处理 - 简化版本"""
        # value参数保留用于向后兼容，但当前实现中未使用
        _ = value  # 标记为已使用以避免警告

        # 使用高频信号处理器调度更新，避免频繁处理
        self.high_freq_handler.schedule_update(
            'scroll_sync',
            self._handle_scroll_sync
        )

    def _handle_scroll_sync(self):
        """智能滚动同步 - 区分用户操作类型"""
        # 如果刚刚发生了导航点击，暂时跳过同步（避免视觉干扰）
        current_time = time.time() * 1000
        if hasattr(self, 'last_navigation_time') and (current_time - self.last_navigation_time) < 1000:  # 1秒内
            return

        # 检查当前是否在显示特殊页面
        current_item = self.category_list.currentItem()
        if current_item:
            current_category = current_item.text()
            if DecisionMapper.is_special_category(current_category):
                return

        # 正常的滚动同步逻辑（仅在用户手动滚动时）
        current_visible_category = self.get_current_visible_category()

        if current_visible_category and current_visible_category != self.last_category:
            # 静默更新导航栏（不触发切换事件）
            for i in range(self.category_list.count()):
                item = self.category_list.item(i)
                if item.text() == current_visible_category:
                    # 断开信号连接，避免触发切换
                    self.category_list.currentItemChanged.disconnect()
                    self.category_list.setCurrentRow(i)
                    # 重新连接信号
                    self.category_list.currentItemChanged.connect(self.on_category_selected)
                    # 更新last_category以避免重复处理
                    self.last_category = current_visible_category
                    break


    def get_current_visible_category(self):
        """获取当前滚动位置对应的分类 - 优化版本"""
        scroll_pos = self.scroll_area.verticalScrollBar().value()
        viewport_height = self.scroll_area.viewport().height()

        # 定义需要检查的分类列表（排除特殊页面）
        categories_to_check = []
        for i in range(self.category_list.count()):
            category_name = self.category_list.item(i).text()
            if not DecisionMapper.is_special_category(category_name):
                categories_to_check.append(category_name)

        # 更新锚点位置缓存（仅在需要时）
        self._update_anchor_positions_cache(categories_to_check)

        # 使用缓存的位置信息查找当前可见分类
        current_category = None
        for category_name in categories_to_check:
            if category_name in self.anchor_positions:
                anchor_pos = self.anchor_positions[category_name]
                # 如果锚点在可视区域内或即将进入可视区域
                if anchor_pos <= scroll_pos + viewport_height // 3:
                    current_category = category_name
                else:
                    break

        return current_category

    def _update_anchor_positions_cache(self, categories):
        """更新锚点位置缓存"""
        # 只在缓存为空或分类数量变化时更新
        if not self.anchor_positions or len(self.anchor_positions) != len(categories):
            self.anchor_positions.clear()
            for category_name in categories:
                anchor_widget = self.scroll_content.findChild(QWidget, f"anchor_{category_name}")
                if anchor_widget:
                    try:
                        anchor_pos = anchor_widget.mapTo(self.scroll_content, QPoint(0, 0)).y()
                        self.anchor_positions[category_name] = anchor_pos
                    except:
                        # 如果计算位置失败，跳过这个锚点
                        pass

    def _clear_scroll_layout(self):
        """清理滚动布局 - 优化版本"""
        # 清理锚点位置缓存
        self.anchor_positions.clear()

        # 标记缓存失效
        self._categories_cache_valid = False

        # 清理布局，但保留特殊页面组件
        while self.scroll_layout.count():
            item = self.scroll_layout.takeAt(0)
            widget = item.widget()
            if widget:
                # 对于特殊页面部件，只隐藏它，不删除
                if (hasattr(self, 'fault_query_embedded_widget') and widget == self.fault_query_embedded_widget) or \
                   (hasattr(self, 'log_management_widget') and widget == self.log_management_widget) or \
                   (hasattr(self, 'chat_robot_widget') and widget == self.chat_robot_widget) or \
                   (hasattr(self, 'settings_page_widget') and widget == self.settings_page_widget) or \
                   (hasattr(self, 'vehicle_status_widget') and widget == self.vehicle_status_widget) or \
                   (hasattr(self, 'community_widget') and widget == self.community_widget) or \
                   (hasattr(self, 'home_page_widget') and widget == self.home_page_widget):
                    widget.hide()
                else:
                    # 对其他部件，正常删除
                    widget.hide()
                    widget.setParent(None)
                    widget.deleteLater()

        # 强制垃圾回收
        MemoryUtils.force_garbage_collection()

    def _restore_cached_layout(self, cache_key):
        """恢复缓存的布局"""
        if cache_key in self.cached_widgets:
            self._clear_scroll_layout()
            for widget in self.cached_widgets[cache_key]:
                widget.show()
                self.scroll_layout.addWidget(widget)
            self.scroll_layout.addStretch()

    # ==================== 基础检查表格显示功能 ====================
    def _add_basic_check_empty_table(self, parent_layout):
        """添加基础检查的动态三栏组容器"""
        from PyQt5.QtWidgets import QGroupBox, QTextEdit

        # 创建垂直布局容器，用于存放多个三栏组
        self.panels_main_container = QWidget()
        self.panels_main_container.setObjectName("basic_check_panels_main")
        self.panels_main_layout = QVBoxLayout(self.panels_main_container)
        self.panels_main_layout.setSpacing(20)  # 组与组之间的间隙
        self.panels_main_layout.setContentsMargins(20, 20, 20, 20)

        # 添加到父布局
        parent_layout.addWidget(self.panels_main_container)

        # 初始化已完成项目集合和面板映射
        self.completed_check_items = set()
        self.active_panels = {}  # 存储活跃面板的映射 {check_item_name: panel_widget}

        # 初始状态不显示任何面板（根据需求）
        self.panels_main_container.hide()

    def _create_single_three_column_group(self, check_item, check_content, standard):
        """创建单个三栏组合"""
        from PyQt5.QtWidgets import QHBoxLayout, QCheckBox

        # 创建组容器
        group_container = QWidget()
        group_container.setStyleSheet("""
            QWidget {
                border: 2px solid #d0d0d0;
                border-radius: 15px;
                background-color: #fafafa;
                margin: 10px;
                padding: 15px;
            }
        """)

        # 创建垂直布局
        group_layout = QVBoxLayout(group_container)
        group_layout.setSpacing(15)
        group_layout.setContentsMargins(15, 15, 15, 15)

        # 创建水平三栏布局
        three_columns_container = QWidget()
        three_columns_layout = QHBoxLayout(three_columns_container)
        three_columns_layout.setSpacing(2)  # 减少间距从15到8
        three_columns_layout.setContentsMargins(0, 0, 0, 0)

        # 创建三个面板
        check_items_panel = self._create_single_panel("检查项目", check_item, "#e3f2fd")
        check_content_panel = self._create_single_panel("检查细则", check_content, "#fff8e1")
        check_standards_panel = self._create_single_panel("相关标准", standard, "#e8f5e8")

        # 设置面板标识以便自适应更新时识别
        check_items_panel.setObjectName("check_items_panel")
        check_content_panel.setObjectName("check_content_panel")
        check_standards_panel.setObjectName("check_standards_panel")

        # 设置自适应管理器引用和初始尺寸
        for panel in [check_items_panel, check_content_panel, check_standards_panel]:
            panel.adaptive_manager = self.adaptive_manager

        # 应用自适应尺寸
        self._apply_check_panels_adaptive_size(check_items_panel, check_content_panel, check_standards_panel)

        # 添加面板到水平布局
        three_columns_layout.addWidget(check_items_panel)
        three_columns_layout.addWidget(check_content_panel)
        three_columns_layout.addWidget(check_standards_panel)

        # 创建勾选框
        checkbox = QCheckBox()
        checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 16px;
                color: #333333;
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border: 2px solid #cccccc;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #4CAF50;
                border-color: #4CAF50;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEzLjMzMzMgNEw2IDExLjMzMzNMMi42NjY2NyA4IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
            QCheckBox::indicator:hover {
                border-color: #4CAF50;
            }
        """)
        checkbox.setFixedSize(30, 30)

        # 存储检查项目信息到勾选框，用于后续处理
        # 这里的check_item是面板内容，我们需要存储实际的应用名称
        # 通过查找当前正在处理的应用数据来获取正确的名称
        actual_check_item_name = getattr(self, '_current_processing_app_name', check_item)
        checkbox.setProperty("check_item", actual_check_item_name)
        checkbox.setProperty("group_container", group_container)

        # 连接勾选框点击事件
        checkbox.clicked.connect(lambda checked: self._on_checkbox_clicked(checkbox, checked))

        # 添加勾选框到布局
        three_columns_layout.addWidget(checkbox)

        # 添加三栏容器到组布局
        group_layout.addWidget(three_columns_container)

        return group_container

    def _on_checkbox_clicked(self, checkbox, checked):
        """处理勾选框点击事件"""
        if checked:
            # 获取检查项目名称
            check_item_name = checkbox.property("check_item")
            group_container = checkbox.property("group_container")

            # 标记该检查项目为已完成
            self._mark_check_item_completed(check_item_name)

            # 隐藏当前面板组
            if group_container:
                group_container.hide()
                # 从布局中移除并删除
                QTimer.singleShot(100, lambda: self._remove_completed_panel(group_container, check_item_name))

    def _mark_check_item_completed(self, check_item):
        """标记检查项目为已完成"""
        # 初始化已完成项目集合（如果不存在）
        if not hasattr(self, 'completed_check_items'):
            self.completed_check_items = set()

        # 添加到已完成集合
        self.completed_check_items.add(check_item.strip())

        # 更新对应的卡片显示勾号
        self._update_card_check_status(check_item)

    def _update_card_check_status(self, check_item):
        """更新卡片的勾选状态显示"""
        # 查找对应的卡片并更新显示
        if hasattr(self, 'scroll_content'):
            # 查找所有AppFrame
            app_frames = self.scroll_content.findChildren(AppFrame)
            for frame in app_frames:
                if frame.app_data.get('name', '').strip() == check_item.strip():
                    # 标记卡片为已完成
                    frame.set_completed_status(True)
                    break

    def _remove_completed_panel(self, group_container, check_item_name):
        """移除已完成的面板组"""
        if group_container and hasattr(self, 'panels_main_layout'):
            # 从布局中移除
            self.panels_main_layout.removeWidget(group_container)
            # 删除组件
            group_container.deleteLater()

            # 从活跃面板映射中移除
            if hasattr(self, 'active_panels') and check_item_name in self.active_panels:
                del self.active_panels[check_item_name]

            # 如果没有活跃面板了，隐藏整个面板容器
            if hasattr(self, 'active_panels') and len(self.active_panels) == 0:
                if hasattr(self, 'panels_main_container'):
                    self.panels_main_container.hide()

    def _apply_check_panels_adaptive_size(self, check_items_panel, check_content_panel, check_standards_panel):
        """应用检查面板的自适应尺寸"""
        if not self.adaptive_manager:
            # 如果没有自适应管理器，使用默认尺寸
            check_items_panel.setMaximumWidth(500)
            check_items_panel.setMinimumWidth(200)
            check_content_panel.setMaximumWidth(700)
            check_content_panel.setMinimumWidth(800)
            check_standards_panel.setMaximumWidth(400)
            check_standards_panel.setMinimumWidth(480)
            return

        # 根据窗口状态设置自适应尺寸
        is_maximized = self.adaptive_manager.current_state == "maximized"

        if is_maximized:
            # 最大化窗口 - 更宽的面板
            check_items_panel.setMaximumWidth(600)
            check_items_panel.setMinimumWidth(250)
            check_content_panel.setMaximumWidth(850)
            check_content_panel.setMinimumWidth(900)
            check_standards_panel.setMaximumWidth(500)
            check_standards_panel.setMinimumWidth(550)
        else:
            # 恢复窗口 - 较窄的面板
            check_items_panel.setMaximumWidth(180)
            check_items_panel.setMinimumWidth(180)
            check_content_panel.setMaximumWidth(380)
            check_content_panel.setMinimumWidth(380)
            check_standards_panel.setMaximumWidth(320)
            check_standards_panel.setMinimumWidth(320)

    def _create_single_panel(self, title, content, bg_color):
        """创建单个面板"""
        from PyQt5.QtWidgets import QGroupBox, QTextEdit

        group = QGroupBox(title)
        group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: 500;
                font-size: 22px;
                border: 1px solid #e0e0e0;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 16px;
                background-color: #ffffff;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 16px;
                padding: 0 8px 0 8px;
                color: #333333;
                background-color: #ffffff;
            }}
        """)

        layout = QVBoxLayout(group)

        text_edit = QTextEdit()
        text_edit.setReadOnly(True)
        text_edit.setMaximumHeight(100)
        text_edit.setMinimumHeight(100)
        text_edit.setStyleSheet(f"""
            QTextEdit {{
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 12px;
                background-color: {bg_color};
                font-size: 18px;
                font-family: "Microsoft YaHei";
                color: #333333;
                line-height: 1.6;
            }}
        """)

        # 设置内容
        text_edit.setText(content if content else "暂无数据")

        layout.addWidget(text_edit)
        return group





    def _show_default_panels_content(self):
        """显示默认的面板内容"""
        # 清空现有的三栏组
        self._clear_existing_groups()

        # 创建默认提示组
        default_group = self._create_single_three_column_group(
            """请点击上方的基础检查卡片查看具体的检查项目

系统将显示：
• 安全防护检查
• 工具设备检查
• 环境安全检查
• 其他检查项目

点击任意卡片开始检查""",
            """请点击上方的基础检查卡片查看具体的检查细则

系统将显示：
• 详细的检查步骤
• 检查要点说明
• 注意事项提醒
• 操作规范指导

选择检查项目后查看详情""",
            """请点击上方的基础检查卡片查看相关的标准

系统将显示：
• 相关标准编号
• 标准适用范围
• 技术要求规范
• 检验方法标准

选择检查项目后查看标准"""
        )

        self.panels_main_layout.addWidget(default_group)

    def _clear_existing_groups(self):
        """清空现有的三栏组"""
        if hasattr(self, 'panels_main_layout'):
            # 删除所有子组件
            while self.panels_main_layout.count():
                child = self.panels_main_layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()

    def show_basic_check_table(self, app_data):
        """显示基础检查面板数据"""
        # 隐藏任何显示的工具提示
        self._hide_all_tooltips()

        # 查找现有的面板容器
        panels_container = self.scroll_content.findChild(QWidget, "basic_check_panels_main")
        if not panels_container:
            # 如果没有找到面板，说明还没有创建
            return

        # 获取检查项目名称
        check_item_name = app_data.get('name', '').strip()

        # 检查该项目是否已完成
        if hasattr(self, 'completed_check_items') and check_item_name in self.completed_check_items:
            # 已完成的项目不显示面板
            return

        # 检查是否已经有该项目的面板
        if hasattr(self, 'active_panels') and check_item_name in self.active_panels:
            # 如果已有面板，不重复创建
            return

        # 显示面板容器（如果之前隐藏）
        if not panels_container.isVisible():
            panels_container.show()

        # 添加新的面板数据
        self._add_single_panel_data(app_data)

        # 取消自动滚动 - 让用户手动控制滚动位置
        # QTimer.singleShot(100, lambda: self._scroll_to_panels())

    def _add_single_panel_data(self, app_data):
        """添加单个面板数据（不清空现有面板）"""
        try:
            check_item_name = app_data.get('name', '').strip()

            # 设置当前处理的应用名称，供勾选框使用
            self._current_processing_app_name = check_item_name

            # 获取description字段数据
            descriptions = []
            # 动态检测有多少个description字段
            i = 1
            while True:
                desc_key = f"description{i}"
                desc_value = app_data.get(desc_key, "")
                if desc_value:
                    # 解析逗号分隔的数据，按照新格式：检查项目,检查细则,相关标准
                    items = [item.strip() for item in desc_value.split(',') if item.strip()]
                    # 确保有3个字段，如果不足则补空
                    while len(items) < 3:
                        items.append("")
                    # 只取前3个字段
                    descriptions.append(items[:3])
                    i += 1
                else:
                    # 如果没有找到description字段，停止查找
                    break

            # 如果没有找到任何description字段，显示错误信息
            if not descriptions:
                error_group = self._create_single_three_column_group(
                    "未找到检查项目数据",
                    "未找到检查细则数据",
                    "未找到相关标准数据"
                )
                self.panels_main_layout.addWidget(error_group)
                # 记录到活跃面板
                if hasattr(self, 'active_panels'):
                    self.active_panels[check_item_name] = error_group
                return

            # 为每个description创建一个独立的三栏组
            for i, desc_data in enumerate(descriptions, 1):
                if len(desc_data) >= 3:
                    check_item = desc_data[0].replace('[', '').replace(']', '').strip()
                    check_content = desc_data[1].replace('[', '').replace(']', '').strip()
                    standard = desc_data[2].replace('[', '').replace(']', '').strip()

                    # 创建三栏组
                    three_column_group = self._create_single_three_column_group(
                        check_item if check_item else "暂无检查项目数据",
                        check_content if check_content else "暂无检查细则数据",
                        standard if standard else "暂无相关标准数据"
                    )

                    # 添加到主布局
                    self.panels_main_layout.addWidget(three_column_group)

                    # 记录到活跃面板（使用第一个面板作为代表）
                    if i == 1 and hasattr(self, 'active_panels'):
                        self.active_panels[check_item_name] = three_column_group

        except Exception as e:
            print(f"添加面板数据时出错: {e}")
            # 设置错误内容
            error_group = self._create_single_three_column_group(
                f"加载 {app_data.get('name', '未知项目')} 数据时出错",
                f"错误信息: {str(e)}",
                "请检查数据格式是否正确"
            )
            self.panels_main_layout.addWidget(error_group)
            # 记录到活跃面板
            if hasattr(self, 'active_panels'):
                self.active_panels[check_item_name] = error_group

    def _fill_panels_data(self, app_data):
        """填充面板数据 - 动态创建多个三栏组"""
        try:
            # 清空现有的三栏组
            self._clear_existing_groups()

            # 获取description字段数据
            descriptions = []
            # 动态检测有多少个description字段
            i = 1
            while True:
                desc_key = f"description{i}"
                desc_value = app_data.get(desc_key, "")
                if desc_value:
                    # 解析逗号分隔的数据，按照新格式：检查项目,检查细则,相关标准
                    items = [item.strip() for item in desc_value.split(',') if item.strip()]
                    # 确保有3个字段，如果不足则补空
                    while len(items) < 3:
                        items.append("")
                    # 只取前3个字段
                    descriptions.append(items[:3])
                    i += 1
                else:
                    # 如果没有找到description字段，停止查找
                    break

            # 如果没有找到任何description字段，显示错误信息
            if not descriptions:
                error_group = self._create_single_three_column_group(
                    "未找到检查项目数据",
                    "未找到检查细则数据",
                    "未找到相关标准数据"
                )
                self.panels_main_layout.addWidget(error_group)
                return

            # 为每个description创建一个独立的三栏组
            for i, desc_data in enumerate(descriptions, 1):
                if len(desc_data) >= 3:
                    check_item = desc_data[0].replace('[', '').replace(']', '').strip()
                    check_content = desc_data[1].replace('[', '').replace(']', '').strip()
                    standard = desc_data[2].replace('[', '').replace(']', '').strip()

                    # 创建三栏组
                    three_column_group = self._create_single_three_column_group(
                        check_item if check_item else "暂无检查项目数据",
                        check_content if check_content else "暂无检查细则数据",
                        standard if standard else "暂无相关标准数据"
                    )

                    # 添加到主布局
                    self.panels_main_layout.addWidget(three_column_group)

        except Exception as e:
            print(f"填充面板数据时出错: {e}")
            # 设置错误内容
            error_group = self._create_single_three_column_group(
                f"加载 {app_data.get('name', '未知项目')} 数据时出错",
                f"错误信息: {str(e)}",
                "请检查数据格式是否正确"
            )
            self.panels_main_layout.addWidget(error_group)

    def _scroll_to_panels(self):
        """滚动到面板位置"""
        # 滚动到底部以显示新添加的面板
        scrollbar = self.scroll_area.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    # ==================== 应用启动功能 ====================
    def launch_app(self, app):
        """启动应用程序"""
        # 隐藏任何显示的工具提示
        self._hide_all_tooltips()

        # Log the action first
        logger = get_logger()
        logger.log_user_action(f"启动应用: {app.get('name', '未知应用')}")

        path = app.get('path', '')
        action = app.get('action')

        if action == 'background_request':
            self.run_background_request(path, app.get('name'))
            return

        if not path:
            QMessageBox.warning(self, "错误", "应用路径不能为空！")
            return
        
        try:
            # 处理内部应用路径
            if path.startswith('internal:'):
                internal_app = path.split(':')[1]
                if internal_app == 'fault_query':
                    # 切换到故障诊断分类视图
                    for i in range(self.category_list.count()):
                        if self.category_list.item(i).text() == "故障诊断系统":
                            self.category_list.setCurrentRow(i)
                            break
                return
                
            # 根据路径类型处理
            if path.lower().startswith(('http://', 'https://')):
                # 网页链接
                webbrowser.open(path)
            elif path.lower().endswith('.py'):
                # 检查是否为flow.py，特殊处理
                if path.endswith('flow.py') or 'flow.py' in path:
                    # 获取当前分类和应用名称
                    category = self.get_current_category()
                    device_name = app.get('name', '')
                    # 调用流程展示页面，传入分类和设备名称
                    self.show_flow_page(category, device_name)
                    print(f"启动流程应用: 分类={category}, 设备={device_name}")
                else:
                    # 普通Python脚本 - 使用无窗口模式启动
                    DETACHED_PROCESS = 0x00000008
                    CREATE_NO_WINDOW = 0x08000000
                    subprocess.Popen(
                        [sys.executable, path],
                        creationflags=DETACHED_PROCESS | CREATE_NO_WINDOW,
                        cwd=os.path.dirname(path) if os.path.dirname(path) else None,
                        shell=False
                    )
            elif os.path.isdir(path):
                # 资料文件夹，打开文件夹内容
                self.open_folder(path, app.get('name', '资料'))
            else:
                # 可执行文件 - 设置正确的工作目录，使用无窗口模式启动
                exe_dir = os.path.dirname(path)
                DETACHED_PROCESS = 0x00000008
                CREATE_NO_WINDOW = 0x08000000
                subprocess.Popen(
                    [path],
                    cwd=exe_dir,
                    creationflags=DETACHED_PROCESS | CREATE_NO_WINDOW,
                    shell=False
                )
            
        except Exception as e:
            QMessageBox.warning(self, "启动失败", f"无法启动应用：{str(e)}")
            
    def open_folder(self, folder_path, app_name=None):
        # Log the action
        logger = get_logger()
        logger.log_user_action(f"打开文件夹: {app_name or os.path.basename(folder_path)}")
        
        # 清理布局
        while self.scroll_layout.count():
            item = self.scroll_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()

        # Save current state including the category that launched this folder view
        # This is crucial for correct return behavior
        if not hasattr(self, 'navigation_history'):
            self.navigation_history = []

        # If previous_state is already a folder, we are navigating deeper
        # Otherwise, we are entering a folder view from a category view
        if not (hasattr(self, 'previous_state') and self.previous_state.get('is_folder_view')):
            self.navigation_history.append({
                'type': 'category_view',
                'category': self.current_category, # The category active before opening folder
                'scroll_pos': self.scroll_area.verticalScrollBar().value(), # Save scroll of main view
                'parent_category': self.current_category # Save parent category
            })
            
            # 在进入文件夹视图时，保持父分类的帮助按钮状态
            if self.current_category in self.help_button_categories:
                self.ai_chat_btn.show()
            else:
                self.ai_chat_btn.hide()

        self.previous_state = {
            'type': 'folder_view',
            'folder_path': folder_path,
            'app_name': app_name,
            'is_folder_view': True # Mark that we are in a folder view
        }
        self.current_category = None # No specific category active within folder view
        self.current_subcategory = None
        
        # 设置文件夹视图UI
        self.scroll_content.setStyleSheet(f"QWidget#scrollContent {{ background-color: #199df5; border-bottom-right-radius: 18px; }}")
        folder_main_container = QFrame()
        folder_main_container.setStyleSheet(f"QFrame {{ background-color: #ffffff; border-radius: 8px; padding: 15px; margin: 10px; }}")
        folder_main_layout = QVBoxLayout(folder_main_container)

        title_container = QWidget()
        title_layout = QHBoxLayout(title_container)
        # 设置标题栏
        back_btn = QPushButton("返回")
        back_btn.setFixedWidth(60)
        back_btn.clicked.connect(self.return_to_previous_view)
        title_layout.addWidget(back_btn)
        
        if app_name:
            title_text = app_name
        else:
            title_text = os.path.basename(folder_path)
        app_title_label = QLabel(title_text)
        app_title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 18px;
                font-family: Microsoft YaHei;
                font-weight: bold;
                color: #2c3e50;
            }}
        """)
        app_title_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(app_title_label, 1)
        
        right_spacer = QWidget()
        right_spacer.setFixedWidth(60)
        title_layout.addWidget(right_spacer)
        folder_main_layout.addWidget(title_container)

        # 列出文件和子文件夹
        try:
            files = []
            subfolders = []
            for item in os.listdir(folder_path):
                item_path = os.path.join(folder_path, item)
                if os.path.isdir(item_path):
                    subfolders.append(item_path)
                else:
                    files.append(item_path)
            
            if files:
                self.display_current_folder_files(files, folder_main_layout)
                if subfolders:
                    separator = QFrame()
                    separator.setFrameShape(QFrame.HLine)
                    separator.setFrameShadow(QFrame.Sunken)
                    separator.setStyleSheet("background-color: #e0e0e0; margin-top: 5px; margin-bottom: 5px;")
                    separator.setFixedHeight(1)
                    folder_main_layout.addWidget(separator)
            
            for i, subfolder_path_item in enumerate(sorted(subfolders)):
                subfolder_name_item = os.path.basename(subfolder_path_item)
                self.display_subfolder_contents(subfolder_path_item, subfolder_name_item, folder_main_layout)
                if i < len(subfolders) - 1:
                    separator = QFrame()
                    separator.setFrameShape(QFrame.HLine)
                    separator.setFrameShadow(QFrame.Sunken)
                    separator.setStyleSheet("background-color: #e0e0e0; margin-top: 5px; margin-bottom: 5px;")
                    separator.setFixedHeight(1)
                    folder_main_layout.addWidget(separator)
        except Exception as e:
            QMessageBox.warning(self, "读取失败", f"无法读取文件夹内容：{str(e)}")
            self.return_to_previous_view() # Go back if error
            return

        self.scroll_layout.addWidget(folder_main_container)
        self.scroll_layout.addStretch()
    
    def display_subfolder_contents(self, subfolder_path, subfolder_name, parent_layout):
        """显示子文件夹的内容，包括文件和嵌套子文件夹"""
        try:
            # 获取子文件夹中的文件和下一级文件夹
            files = []
            nested_folders = []
            
            for item in os.listdir(subfolder_path):
                item_path = os.path.join(subfolder_path, item)
                if os.path.isdir(item_path):
                    nested_folders.append(item_path)
                else:
                    files.append(item_path)
            
            # 如果没有文件和嵌套文件夹，直接返回
            if not files and not nested_folders:
                return
                
            # 创建子文件夹区域容器（带边框）
            subfolder_frame = QFrame()
            subfolder_frame.setStyleSheet(f"""
                QFrame {{
                    border: 1px solid #e0e0e0;
                    border-radius: 12px;
                    background-color: #ffffff;
                    padding: 15px;
                    margin-bottom: 20px;
                }}
            """)
            subfolder_layout = QVBoxLayout(subfolder_frame)
            subfolder_layout.setContentsMargins(15, 15, 15, 15)
            subfolder_layout.setSpacing(15)
            
            # 标题行
            title_container = QWidget()
            title_layout = QHBoxLayout(title_container)
            title_layout.setContentsMargins(0, 0, 0, 5)
            
            # 文件夹标题
            title = QLabel(subfolder_name)
            title.setStyleSheet(f"""
                QLabel {{
                    font-size: 16px;
                    color: #34495e;
                    font-weight: bold;
                }}
            """)
            title_layout.addWidget(title)
            title_layout.addStretch()
            
            subfolder_layout.addWidget(title_container)
            
            # 如果没有文件但有嵌套文件夹，显示提示信息
            if not files and nested_folders:
                info_layout = QHBoxLayout()
                info_layout.setContentsMargins(0, 0, 0, 5)

                info_label = QLabel("此文件夹没有直接文件，但包含子文件夹")
                info_label.setStyleSheet(f"color: #7f8c8d; font-style: italic;")
                info_layout.addWidget(info_label)
                
                open_btn = QPushButton("打开")
                open_btn.setFixedWidth(60)
                open_btn.clicked.connect(lambda: self.open_folder(subfolder_path, subfolder_name))
                open_btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: #3498db;
                        color: white;
                        font-family: Microsoft YaHei;
                        font-size: 14px;
                        border-radius: 3px;
                        padding: 3px;
                    }}
                    QPushButton:hover {{
                        background-color: #2980b9;
                    }}
                """)
                info_layout.addWidget(open_btn)
                
                info_widget = QWidget()
                info_widget.setLayout(info_layout)
                subfolder_layout.addWidget(info_widget)
            
            # 创建网格布局显示文件
            if files:
                grid_container = LayoutFactory.create_file_grid(files, None, self)
                subfolder_layout.addWidget(grid_container)
            
            # 如果有嵌套文件夹且有文件，显示一个提示
            if nested_folders and files:
                nested_container = QWidget()
                nested_layout = QHBoxLayout(nested_container)
                nested_layout.setContentsMargins(0, 5, 0, 0)
                
                info_text = f"包含{len(nested_folders)}个子文件夹"
                nested_label = QLabel(info_text)
                nested_label.setStyleSheet(f"color: #2980b9;")
                nested_layout.addWidget(nested_label)
                
                open_btn = QPushButton("浏览子文件夹")
                open_btn.setFixedWidth(100)
                open_btn.clicked.connect(lambda: self.open_folder(subfolder_path, subfolder_name))
                open_btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: #3498db;
                        color: white;
                        border-radius: 3px;
                        padding: 3px;
                    }}
                    QPushButton:hover {{
                        background-color: #2980b9;
                    }}
                """)
                nested_layout.addWidget(open_btn)
                nested_layout.addStretch()
                
                subfolder_layout.addWidget(nested_container)
            
            parent_layout.addWidget(subfolder_frame)
            
        except Exception as e:
            error_label = QLabel(f"无法读取子文件夹 {subfolder_name}: {str(e)}")
            error_label.setStyleSheet(f"""
                color: red;
                font-family: Microsoft YaHei;
                font-size: 14px;
                font-weight: bold;
            """)
            parent_layout.addWidget(error_label)
    
    def return_to_previous_view(self):
        """返回到之前的视图（分类视图或上一级文件夹）"""
        if hasattr(self, 'navigation_history') and self.navigation_history:
            last_view = self.navigation_history.pop()
            
            if last_view['type'] == 'category_view':
                self.current_category = last_view['category']
                # 使用单独显示模式重新绘制分类视图
                if not DecisionMapper.is_special_category(self.current_category):
                    self.display_single_category(self.current_category)
                else:
                    self.display_all_categories()
                
                # 暂停自动触发事件，防止导航混乱
                self.category_list.blockSignals(True)
                
                # Restore category selection in list
                if self.current_category:
                    for i in range(self.category_list.count()):
                        if self.category_list.item(i).text() == self.current_category:
                            self.category_list.setCurrentRow(i)
                            break
                
                # 恢复信号处理
                self.category_list.blockSignals(False)
                
                # 根据返回的分类设置帮助按钮可见性
                if self.current_category in self.help_button_categories:
                    self.ai_chat_btn.show()
                else:
                    self.ai_chat_btn.hide()
                
                # 使用定时器延迟滚动操作，确保界面完全渲染后再滚动
                saved_pos = last_view.get('scroll_pos', 0)
                delayed_scroll_to_position(self.scroll_area, saved_pos)
                
                self.previous_state = {}
                
            # 可选：处理返回到上一个文件夹的情况
            # elif last_view['type'] == 'folder_view':
            #    self.open_folder(last_view['folder_path'], last_view['app_name'])

        else: # 如果历史为空或损坏，回退到显示第一个分类
            if self.category_list.count() > 0:
                self.category_list.setCurrentRow(0)
                first_category = self.category_list.item(0).text()
                if not DecisionMapper.is_special_category(first_category):
                    self.display_single_category(first_category)
                else:
                    self.display_all_categories()
                
                # 默认情况下隐藏帮助按钮，除非当前分类在指定列表中
                current_item = self.category_list.currentItem()
                if current_item and current_item.text() in self.help_button_categories:
                    self.ai_chat_btn.show()
                else:
                    self.ai_chat_btn.hide()
        
        # 清除以前的文件夹视图状态
        if hasattr(self, 'previous_state') and self.previous_state.get('is_folder_view'):
            self.previous_state = {}
    
    def load_config(self):
        """加载配置，优先使用缓存"""
        try:
            # 使用缓存系统加载应用数据
            return startup_cache.load_apps_data(self.config_file)
        except Exception:
            # 降级到原始方法
            if os.path.exists(self.config_file):
                try:
                    with open(self.config_file, 'r', encoding='utf-8') as f:
                        return json.load(f)
                except:
                    return {}
            return {}

    def load_config_optimized(self):
        """优化的配置加载方法"""
        # 使用缓存管理器
        cache_key = f"config_{self.config_file}"

        def compute_config():
            config_data = self.load_config()
            # 使用高效数据处理过滤无效应用
            return PerformanceUtils.filter_apps_efficiently(config_data)

        return self.cache_manager.get_or_compute(cache_key, compute_config)

    def load_config_async(self, callback):
        """异步加载配置"""
        def load_task():
            return self.load_config_optimized()

        return self.thread_manager.run_in_background(load_task, callback)
        
    def save_config(self):
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.apps_data, f, ensure_ascii=False, indent=4)
            
    def on_category_changed(self, category):
        self.current_category = category
        self.current_subcategory = None
        # 使用单独显示模式
        if not DecisionMapper.is_special_category(category):
            self.display_single_category(category)
        else:
            self.display_all_categories()

    def resizeEvent(self, event):
        """处理窗口大小变化事件"""
        super().resizeEvent(event)
        
        # 更新AI对话按钮位置
        if hasattr(self, 'ai_chat_container'):
            # 调整位置，更靠左一点
            self.ai_chat_container.move(self.width() - 100, self.height() - 80)

        # 检查窗口最大化状态是否改变
        current_maximized = self.isMaximized()
        if current_maximized != self.is_maximized:
            self.is_maximized = current_maximized
            # 使用自适应管理器获取列数
            self.max_cols = self.adaptive_manager.get_max_cols()

            # 如果当前显示的是应用列表，则重新加载布局
            if self.category_list.currentItem() and self.category_list.currentItem().text() not in ["状态监控系统", "首页", "故障诊断系统", "流程生成助手", "社区", "日志管理", "主题设置"]:
                # 保存当前滚动位置
                current_scroll_pos = self.scroll_area.verticalScrollBar().value()
                # 重新显示当前分类，使用单独显示模式
                current_category = self.category_list.currentItem().text()
                if not DecisionMapper.is_special_category(current_category):
                    self.display_single_category(current_category)
                else:
                    self.display_all_categories()
                # 滚动到之前的位置
                delayed_scroll_to_position(self.scroll_area, current_scroll_pos)
                
    def changeEvent(self, event):
        """处理窗口状态变化事件"""
        if event.type() == QEvent.WindowStateChange:
            # 检查窗口最大化状态是否改变
            current_maximized = self.isMaximized()
            if current_maximized != self.is_maximized:
                self.is_maximized = current_maximized
                # 使用自适应管理器获取列数
                self.max_cols = self.adaptive_manager.get_max_cols()

                # 如果当前显示的是应用列表，则重新加载布局
                if self.category_list.currentItem() and self.category_list.currentItem().text() not in ["状态监控系统", "首页", "故障诊断系统", "流程生成助手",  "社区", "日志管理", "主题设置"]:
                    # 保存当前滚动位置
                    current_scroll_pos = self.scroll_area.verticalScrollBar().value()
                    # 重新显示当前分类，使用单独显示模式
                    current_category = self.category_list.currentItem().text()
                    if not DecisionMapper.is_special_category(current_category):
                        self.display_single_category(current_category)
                    else:
                        self.display_all_categories()
                    # 滚动到之前的位置
                    delayed_scroll_to_position(self.scroll_area, current_scroll_pos)
        elif event.type() == QEvent.ActivationChange:
            # 窗口激活状态改变时，确保最大化状态正确
            if self.isActiveWindow():
                self._refresh_window_state()

        super().changeEvent(event)

    def _refresh_window_state(self):
        """刷新窗口状态，确保最大化状态和导航栏状态正确"""
        # 更新最大化状态
        current_maximized = self.isMaximized()
        if current_maximized != self.is_maximized:
            self.is_maximized = current_maximized
            # 使用自适应管理器获取列数
            self.max_cols = self.adaptive_manager.get_max_cols()

    def focusInEvent(self, event):
        """窗口获得焦点时的处理"""
        super().focusInEvent(event)
        # 确保窗口状态正确
        self._refresh_window_state()

    def closeEvent(self, event):
        """Handle the window close event."""
        # Log the end of the last active feature
        if self.current_active_feature:
            get_logger().log_feature_end(self.current_active_feature)

        # 清理所有后台线程
        if hasattr(self, 'thread_manager'):
            self.thread_manager.terminate_all()

        # 清理所有资源
        if hasattr(self, 'resource_manager'):
            self.resource_manager.cleanup_all()

        # 清理弱引用
        if hasattr(self, 'weak_ref_manager'):
            self.weak_ref_manager.clear_all()

        # 清理缓存
        if hasattr(self, 'cache_manager'):
            self.cache_manager.clear()

        # 清理信号优化器
        if hasattr(self, 'signal_optimizer'):
            self.signal_optimizer.disconnect_all()

        # 停止高频信号处理器
        if hasattr(self, 'high_freq_handler'):
            self.high_freq_handler.stop()

        # 清理丝滑导航处理器
        if hasattr(self, 'smooth_nav_handler'):
            self.smooth_nav_handler.switch_timer.stop()

        # 停止滚动动画
        if hasattr(self, 'smooth_scroll_animation') and self.smooth_scroll_animation.state() == QPropertyAnimation.Running:
            self.smooth_scroll_animation.stop()

        # 强制垃圾回收
        MemoryUtils.force_garbage_collection()

        super().closeEvent(event)

    # ==================== 页面显示功能 ====================
    def show_home_page(self):
        """显示首页界面 - 简化版本"""
        # 清空滚动区域内容
        self._clear_scroll_layout()

        # 使分类缓存失效，因为我们现在显示的是首页而不是分类列表
        self._categories_cache_valid = False

        # 设置滚动区域的背景样式为白色（首页背景）
        self.scroll_content.setStyleSheet(f"""
            QWidget#scrollContent {{
                background-color: #ffffff;
                border-bottom-right-radius: 18px;
            }}
        """)

        # 创建首页界面（简化，不使用预加载）
        self.home_page_widget = HomePageWidget(self)
        # 连接导航信号
        self.home_page_widget.navigate_to_category.connect(self.navigate_to_category)
        self.home_page_widget.launch_app.connect(self.launch_app)

        # 添加首页界面
        self.scroll_layout.addWidget(self.home_page_widget)

        # 滚动到顶部
        self.scroll_area.verticalScrollBar().setValue(0)

    def show_category_by_name(self, category_name):
        """根据分类名称显示对应界面"""
        # 确保分类列表已经初始化
        if not hasattr(self, 'category_list') or self.category_list.count() == 0:
            QTimer.singleShot(200, lambda: self.show_category_by_name(category_name))
            return

        # 在分类列表中查找并选择对应项目
        for i in range(self.category_list.count()):
            item = self.category_list.item(i)
            if item.text() == category_name:
                self.category_list.setCurrentItem(item)
                self.on_category_selected(item)
                return

    def show_vehicle_status(self):
        """显示传感器状态监控系统页面 - 优化版本"""
        # 清理现有内容
        self._clear_scroll_layout()

        # 使分类缓存失效
        self._categories_cache_valid = False

        # 按需创建组件
        widget = self._get_or_create_vehicle_status_widget()
        if widget is None:
            # 创建失败，显示错误信息
            error_widget = QLabel("无法加载传感器监控页面")
            error_widget.setAlignment(Qt.AlignCenter)
            error_widget.setStyleSheet("color: red; font-size: 16px; padding: 20px;")
            self.scroll_layout.addWidget(error_widget)
            return

        # 将组件添加到布局中
        widget.setParent(self.scroll_content)
        self.scroll_layout.addWidget(widget)
        widget.show()

        # 设置样式
        self.scroll_area.setStyleSheet("background-color: transparent; border: none;")
        self.scroll_area.widget().setStyleSheet("background-color: transparent;")

        # 添加弹性空间
        self.scroll_layout.addStretch(1)

        # 确保导航栏中的正确项目被选中
        for i in range(self.category_list.count()):
            if self.category_list.item(i).text() == "状态监控系统":
                self.category_list.blockSignals(True)
                self.category_list.setCurrentRow(i)
                self.category_list.blockSignals(False)
                break

    def show_chat_robot_page(self):
        """显示聊天机器人页面"""
        # Log the action
        get_logger().log_feature_start("聊天机器人")
        get_logger().log_user_action("点击导航栏 '聊天机器人'")
        
        # 清理现有内容
        self._clear_scroll_layout()

        # 使分类缓存失效
        self._categories_cache_valid = False

        # 设置滚动区域的背景样式
        self.scroll_content.setStyleSheet(f"""
            QWidget#scrollContent {{
                background-color: #f8f9fa; /* Or any other suitable color */
                border-bottom-right-radius: 18px;
            }}
        """)
        
        # 预创建聊天机器人页面实例，以便在用户点击时立即显示
        if not hasattr(self, 'chat_robot_widget') or self.chat_robot_widget is None:
            # 第一次创建时，可能会有短暂延迟
            self.chat_robot_widget = ChatRobotWidget(self)
            # 设置自适应管理器引用
            self.chat_robot_widget.adaptive_manager = self.adaptive_manager
            self.scroll_layout.addWidget(self.chat_robot_widget)
        else:
            # 如果widget已存在，确保它可见并在布局中
            self.chat_robot_widget.show()
            # 检查是否需要重新添加到布局中
            if self.chat_robot_widget.parent() is None:
                self.scroll_layout.addWidget(self.chat_robot_widget)
            elif self.scroll_layout.indexOf(self.chat_robot_widget) < 0:
                self.scroll_layout.addWidget(self.chat_robot_widget)

    def show_fault_query_page(self):
        # 清理滚动布局中的现有内容
        self._clear_scroll_layout()

        # 使分类缓存失效
        self._categories_cache_valid = False

        # Set a consistent background for the content area
        self.scroll_content.setStyleSheet(f"""
            QWidget#scrollContent {{
                background-color: #f8f9fa; /* Or your preferred page background */
                border-bottom-right-radius: 18px;
            }}
        """)

        # Instantiate and add the fault query widget (MainApplicationWindow from fault_query.py)
        if not hasattr(self, 'fault_query_embedded_widget') or self.fault_query_embedded_widget is None:
            self.fault_query_embedded_widget = MainApplicationWindow() # MainApplicationWindow is now a QWidget
            self.scroll_layout.addWidget(self.fault_query_embedded_widget)
        else:
            # 如果widget已存在，确保它可见并在布局中
            self.fault_query_embedded_widget.show()
            # 检查是否需要重新添加到布局中
            if self.fault_query_embedded_widget.parent() is None:
                self.scroll_layout.addWidget(self.fault_query_embedded_widget)
            elif self.scroll_layout.indexOf(self.fault_query_embedded_widget) < 0:
                self.scroll_layout.addWidget(self.fault_query_embedded_widget)



    # ==================== 辅助方法 ====================
    def _display_category_with_subcategories(self, data, parent_layout):
        """显示有子分类结构的分类"""
        # 显示每个子分类的应用
        for subcategory, apps in data.items():
            # 创建子分类容器（带边框）
            subcategory_frame = QFrame()
            subcategory_frame.setStyleSheet(f"""
                QFrame {{
                    border: none;
                    border-radius: 8px;
                    background-color: rgba(255, 255, 255, 0.1);
                    padding: 10px;
                    margin-bottom: 15px;
                }}
            """)
            subcategory_layout = QVBoxLayout(subcategory_frame)
            subcategory_layout.setContentsMargins(10, 10, 10, 10)
            subcategory_layout.setSpacing(8)
            
            # 子分类标题
            title_container = QWidget()
            title_layout = QHBoxLayout(title_container)
            title_layout.setContentsMargins(0,0,0,2)
            subcategory_title_label = QLabel(subcategory)
            subcategory_title_label.setStyleSheet(f"""
                font-size: 18px;
                font-family: Microsoft YaHei;
                font-weight: bold;
                color: white;
                text-align: center;
            """)
            subcategory_title_label.setAlignment(Qt.AlignCenter)
            title_layout.addWidget(subcategory_title_label, 1)  # 设置stretch因子1，使标题居中
            subcategory_layout.addWidget(title_container)
            
            # 创建子分类的应用网格
            if apps:
                grid_container = LayoutFactory.create_grid_with_apps(apps, None, self)
                subcategory_layout.addWidget(grid_container)
            
            # 将整个子分类框添加到父布局
            parent_layout.addWidget(subcategory_frame)
            
    def _display_category_grid(self, apps, parent_layout, category_name=None):
        """显示普通网格布局的分类"""
        if not isinstance(apps, list):
            apps = []

        # 创建分类容器（带边框）
        category_frame = QFrame()
        category_frame.setStyleSheet(f"""
            QFrame {{
                border: none;
                border-radius: 8px;
                background-color: rgba(255, 255, 255, 0.1);
                padding: 5px 10px 10px 10px;  /* 顶部padding减少到5px，其他保持10px */
                margin-bottom: 15px;
            }}
        """)
        category_layout = QVBoxLayout(category_frame)
        # 调整边距：左、顶部、右、底部 - 进一步减少顶部边距
        category_layout.setContentsMargins(10, -2, 10, 10)  # 顶部边距改为2px，更紧凑
        category_layout.setSpacing(8)

        # 创建应用显示容器
        if apps:
            if category_name == "基础检查":
                # 基础检查使用分类折叠容器（支持分类折叠和智能卡片功能）
                collapsible_container = LayoutFactory.create_collapsible_cards(apps, None, None, self)
                category_layout.addWidget(collapsible_container)
            else:
                # 其他分类使用传统网格布局
                grid_container = LayoutFactory.create_grid_with_apps(apps, None, self)
                category_layout.addWidget(grid_container)

        # 将整个分类框添加到父布局
        parent_layout.addWidget(category_frame)

        # 如果是基础检查分类，添加空白表格
        if category_name == "基础检查":
            self._add_basic_check_empty_table(parent_layout)

    def display_current_folder_files(self, files, parent_layout):
        """显示当前文件夹内的文件"""
        # 创建文件区域标题
        title_label = QLabel("文件")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 18px;
                font-family: Microsoft YaHei;
                font-weight: bold;
                color: #34495e;
                padding-bottom: 5px;
            }}
        """)
        parent_layout.addWidget(title_label)
        
        # 创建网格容器
        grid_container = LayoutFactory.create_file_grid(files, None, self)
        parent_layout.addWidget(grid_container)

    def show_log_management_page(self):
        """显示日志管理页面"""
        
        # 清空滚动区域内容
        self._clear_scroll_layout()  # 使用统一的方法清空布局，保留特殊widget

        # 使分类缓存失效
        self._categories_cache_valid = False

        # 设置滚动区域的背景样式
        self.scroll_content.setStyleSheet(f"""
            QWidget#scrollContent {{
                background-color: #f8f9fa;
                border-bottom-right-radius: 18px;
            }}
        """)
        
        # 预创建日志管理页面实例，以便在用户点击时立即显示
        if not hasattr(self, 'log_management_widget') or self.log_management_widget is None:
            # 第一次创建时，可能会有短暂延迟
            self.log_management_widget = LogManagementWidget(self)
            self.scroll_layout.addWidget(self.log_management_widget)
        else:
            # 如果widget已存在，确保它可见并在布局中
            self.log_management_widget.show()
            # 检查是否需要重新添加到布局中
            if self.log_management_widget.parent() is None:
                self.scroll_layout.addWidget(self.log_management_widget)
            elif self.scroll_layout.indexOf(self.log_management_widget) < 0:
                self.scroll_layout.addWidget(self.log_management_widget)
    
    def show_settings_page(self):
        """显示设置页面 - 优化版本"""
        # 清空滚动区域内容
        self._clear_scroll_layout()

        # 使分类缓存失效
        self._categories_cache_valid = False

        # 设置背景样式
        self.scroll_content.setStyleSheet(f"""
            QWidget#scrollContent {{
                background-color: #f8f9fa;
                border-bottom-right-radius: 18px;
            }}
        """)

        # 按需创建设置页面
        widget = self._get_or_create_settings_widget()
        if widget:
            widget.show()
            self.scroll_layout.addWidget(widget)

    def navigate_to_category(self, category_name):
        """从首页导航到特定分类"""
        for i in range(self.category_list.count()):
            if self.category_list.item(i).text() == category_name:
                self.category_list.setCurrentRow(i)
                break

    def run_background_request(self, url, app_name):
        if requests is None:
            QMessageBox.critical(self, "缺少依赖", "请先安装 'requests' 库 (pip install requests) 来使用此功能。")
            return

        def request_task():
            response = requests.get(url, timeout=5)
            return response.status_code, response

        def on_success(result):
            status_code, _ = result  # 忽略response对象，只使用status_code
            if status_code == 200:
                UIUtils.show_delayed_message(self, "info", "操作成功", f"已成功触发 '{app_name}'。")
            else:
                UIUtils.show_delayed_message(self, "warning", "操作失败", f"触发 '{app_name}' 失败，服务器返回状态码: {status_code}")

        def on_error(error_msg):
            UIUtils.show_delayed_message(self, "warning", "网络错误", f"触发 '{app_name}' 时发生网络错误: {error_msg}")

        self.thread_manager.run_in_background(request_task, on_success, on_error)

    def show_flow_page(self, category=None, device_name=None):
        """显示流程展示页面"""
        # 隐藏任何显示的工具提示
        self._hide_all_tooltips()

        # 隐藏左下角的帮助按钮（图片占位符）
        if hasattr(self, 'ai_chat_btn'):
            self.ai_chat_btn.hide()

        # 清空滚动区域内容
        self._clear_scroll_layout()

        # 使分类缓存失效
        self._categories_cache_valid = False

        # 设置背景样式
        self.scroll_content.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #1e3a8a, stop: 0.5 #3730a3, stop: 1 #1e40af);
                border-radius: 10px;
            }
        """)

        # 创建流程页面组件，传入分类和设备名称参数
        from flow import FlowWidget
        self.flow_widget = FlowWidget(self, category, device_name)
        self.scroll_layout.addWidget(self.flow_widget)

        print(f"显示流程页面: 分类={category}, 设备={device_name}")

    def get_current_category(self):
        """获取当前选中的分类名称"""
        if self.category_list.currentItem():
            return self.category_list.currentItem().text()
        return None

    def _hide_all_tooltips(self):
        """隐藏所有显示的工具提示"""
        if AppFrame.tooltip_widget and AppFrame.tooltip_widget.isVisible():
            AppFrame.tooltip_widget.hide()
        if AppFrame.active_tooltip:
            AppFrame.active_tooltip.tooltip_shown = False
            AppFrame.active_tooltip = None

    def switch_to_category(self, category_name):
        """切换到指定分类"""
        print(f"切换到分类: {category_name}")

        # 查找对应的分类项
        for i in range(self.category_list.count()):
            item = self.category_list.item(i)
            if item.text() == category_name:
                # 选中该分类
                self.category_list.setCurrentItem(item)
                # 触发分类切换事件，传递当前选中的项
                self.on_category_selected(item)
                print(f"成功切换到分类: {category_name}")
                return

        print(f"未找到分类: {category_name}")

    def show_community_page(self):
        """显示社区页面"""
        # 清空滚动区域内容
        self._clear_scroll_layout()

        # 使分类缓存失效
        self._categories_cache_valid = False

        # 设置滚动区域的背景样式
        self.scroll_content.setStyleSheet(f"""
            QWidget#scrollContent {{
                background-color: #3a7bd9;
                border-bottom-right-radius: 18px;
            }}
        """)

        # 创建社区页面组件
        if not hasattr(self, 'community_widget') or self.community_widget is None:
            self.community_widget = CommunityWidget(self)
            self.scroll_layout.addWidget(self.community_widget)
        else:
            # 如果widget已存在，确保它可见并在布局中
            self.community_widget.show()
            # 检查是否需要重新添加到布局中
            if self.community_widget.parent() is None:
                self.scroll_layout.addWidget(self.community_widget)
            elif self.scroll_layout.indexOf(self.community_widget) < 0:
                self.scroll_layout.addWidget(self.community_widget)






