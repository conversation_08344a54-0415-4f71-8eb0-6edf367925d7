import os
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                           QPushButton, QFrame, QMessageBox, QSizePolicy)
from PyQt5.QtGui import QFont
from PyQt5.QtCore import Qt

class SettingsPage(QWidget):
    """设置页面 - 简化版本，不再管理颜色配置"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # 设置页面背景
        self.setStyleSheet("QWidget { background-color: #f8f9fa; }")

        # 标题
        title_label = QLabel("设置")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 28px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # 信息卡片
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 12px;
                padding: 30px;
            }
        """)
        info_layout = QVBoxLayout(info_frame)

        # 信息文本
        info_label = QLabel("颜色配置管理功能已移除")
        info_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #34495e;
                margin-bottom: 15px;
            }
        """)
        info_label.setAlignment(Qt.AlignCenter)
        info_layout.addWidget(info_label)

        desc_label = QLabel("""
        为了简化应用程序的维护和提高性能，颜色配置管理功能已经被移除。
        所有的颜色设置现在直接内嵌在各个组件中。

        如果您需要自定义界面外观，请联系开发者获取支持。
        """)
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6c757d;
                line-height: 1.6;
            }
        """)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setWordWrap(True)
        info_layout.addWidget(desc_label)

        main_layout.addWidget(info_frame)

        # 添加弹性空间
        main_layout.addStretch()

        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        close_btn = QPushButton("关闭")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QPushButton:pressed {
                background-color: #545b62;
            }
        """)
        close_btn.clicked.connect(self.close_settings)
        button_layout.addWidget(close_btn)

        button_layout.addStretch()
        main_layout.addLayout(button_layout)

    def close_settings(self):
        """关闭设置页面"""
        if self.parent():
            # 如果有父窗口，尝试切换到首页
            parent = self.parent()
            if hasattr(parent, 'show_home_page'):
                parent.show_home_page()
            elif hasattr(parent, 'switch_to_home'):
                parent.switch_to_home()