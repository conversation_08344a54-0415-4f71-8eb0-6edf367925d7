#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动缓存系统 - 优化程序启动速度
"""

import os
import json
import pickle
import time
from pathlib import Path

class StartupCache:
    """启动缓存管理器"""
    
    def __init__(self, cache_dir="cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # 缓存文件路径
        self.apps_cache_file = self.cache_dir / "apps_data.cache"
        self.ui_cache_file = self.cache_dir / "ui_elements.cache"
        self.config_cache_file = self.cache_dir / "config.cache"
        
        # 缓存有效期（秒）
        self.cache_validity = 24 * 60 * 60  # 24小时
    
    def is_cache_valid(self, cache_file, source_file=None):
        """检查缓存是否有效"""
        if not cache_file.exists():
            return False
        
        cache_time = cache_file.stat().st_mtime
        current_time = time.time()
        
        # 检查缓存是否过期
        if current_time - cache_time > self.cache_validity:
            return False
        
        # 如果有源文件，检查源文件是否更新
        if source_file and Path(source_file).exists():
            source_time = Path(source_file).stat().st_mtime
            if source_time > cache_time:
                return False
        
        return True
    
    def load_apps_data(self, config_file="apps_config.json"):
        """加载应用数据，优先使用缓存"""
        if self.is_cache_valid(self.apps_cache_file, config_file):
            try:
                with open(self.apps_cache_file, 'rb') as f:
                    return pickle.load(f)
            except Exception:
                pass
        
        # 缓存无效，重新加载
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                apps_data = json.load(f)
            
            # 保存到缓存
            with open(self.apps_cache_file, 'wb') as f:
                pickle.dump(apps_data, f)
            
            return apps_data
        except Exception as e:
            print(f"加载应用配置失败: {e}")
            return {}
    
    def cache_ui_elements(self, elements_data):
        """缓存UI元素数据"""
        try:
            with open(self.ui_cache_file, 'wb') as f:
                pickle.dump(elements_data, f)
        except Exception as e:
            print(f"缓存UI元素失败: {e}")
    
    def load_ui_elements(self):
        """加载UI元素缓存"""
        if self.is_cache_valid(self.ui_cache_file):
            try:
                with open(self.ui_cache_file, 'rb') as f:
                    return pickle.load(f)
            except Exception:
                pass
        return None
    
    def cache_config(self, config_data):
        """缓存配置数据"""
        try:
            with open(self.config_cache_file, 'wb') as f:
                pickle.dump(config_data, f)
        except Exception as e:
            print(f"缓存配置失败: {e}")
    
    def load_config(self):
        """加载配置缓存"""
        if self.is_cache_valid(self.config_cache_file):
            try:
                with open(self.config_cache_file, 'rb') as f:
                    return pickle.load(f)
            except Exception:
                pass
        return None
    
    def clear_cache(self):
        """清理所有缓存"""
        for cache_file in [self.apps_cache_file, self.ui_cache_file, self.config_cache_file]:
            if cache_file.exists():
                try:
                    cache_file.unlink()
                except Exception as e:
                    print(f"删除缓存文件失败 {cache_file}: {e}")
    
    def get_cache_info(self):
        """获取缓存信息"""
        info = {}
        for name, cache_file in [
            ("apps_data", self.apps_cache_file),
            ("ui_elements", self.ui_cache_file),
            ("config", self.config_cache_file)
        ]:
            if cache_file.exists():
                stat = cache_file.stat()
                info[name] = {
                    "exists": True,
                    "size": stat.st_size,
                    "modified": time.ctime(stat.st_mtime),
                    "valid": self.is_cache_valid(cache_file)
                }
            else:
                info[name] = {"exists": False}
        
        return info

# 全局缓存实例
startup_cache = StartupCache()
