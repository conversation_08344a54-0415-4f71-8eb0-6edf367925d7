import socket
from threading import Thread
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                           QFrame, QPushButton, QGridLayout, QComboBox, QDialog,
                           QSizePolicy, QSpacerItem)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QDateTime, QSize
from PyQt5.QtGui import QColor, QPainter, QPen, QIcon, QPixmap
from deploy_dialog import DeployDialog
from device_manager import DeviceManagerDialog
from log_management import get_logger # Import the logger
import json
import os
import time

class StatusIndicator(QFrame):
    def __init__(self, name, icon_path, parent=None):
        super().__init__(parent)
        self.name = name
        self.status = False
        self.value = ""
        # 调整大小策略，使卡片可以自适应扩展
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        # 设置最小尺寸，确保卡片不会过小（调整为3列布局）
        self.setMinimumSize(160, 200)
        self.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border-radius: 8px;
                border: 1px solid #3a7bd9;
                padding: 10px;
            }
            QFrame:hover {
                border: 1px solid #3498db;
            }
            /* 确保内部元素不受外框样式影响 */
            QFrame > QWidget, QFrame > QLabel {
                border: none;
            }
        """)

        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(12)

        # 优化图标显示
        self.icon_label = QLabel()
        self.icon_label.setFixedSize(QSize(60, 60))  # 增大图标区域
        self.icon_label.setAlignment(Qt.AlignCenter)
        self.icon_label.setStyleSheet("background-color: transparent; padding: 0px;")  # 确保没有额外的内边距
        
        # 使用QPainter精确绘制图标
        pixmap = QPixmap(icon_path)
        if not pixmap.isNull():
            # 调整图标大小，确保在边界内完整显示
            scaled_pixmap = pixmap.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            # 计算居中位置
            x_offset = (60 - scaled_pixmap.width()) // 2
            y_offset = (60 - scaled_pixmap.height()) // 2
            
            # 创建空白图像
            final_pixmap = QPixmap(60, 60)
            final_pixmap.fill(Qt.transparent)
            
            # 在空白图像上绘制缩放后的图标
            painter = QPainter(final_pixmap)
            painter.drawPixmap(x_offset, y_offset, scaled_pixmap)
            painter.end()
            
            self.icon_label.setPixmap(final_pixmap)
        else:
            print(f"Warning: Could not load icon at {icon_path}")
            # 创建更美观的占位图标
            fallback_pixmap = QPixmap(60, 60)
            fallback_pixmap.fill(Qt.transparent)
            painter = QPainter(fallback_pixmap)
            # 确保矩形框使用原有的灰色 #7f8c8d
            painter.setPen(QPen(QColor("#7f8c8d"), 2))
            painter.drawRect(10, 10, 40, 40)
            painter.setPen(QPen(QColor("#7f8c8d"), 1))
            painter.drawText(fallback_pixmap.rect(), Qt.AlignCenter, "?")
            painter.end()
            self.icon_label.setPixmap(fallback_pixmap)
            
        main_layout.addWidget(self.icon_label)

        # Text Layout (Name, Status, Update Time)
        text_widget = QWidget() # Use a widget to apply specific stylesheet if needed
        text_widget.setStyleSheet("border: 1px solid #e0e0e0;") # 这里设置明确的边框颜色为原来的#e0e0e0
        text_layout = QVBoxLayout(text_widget)
        text_layout.setContentsMargins(30, 30, 30, 30)
        text_layout.setSpacing(15) # 增加垂直间距

        self.name_label = QLabel(name)
        self.name_label.setWordWrap(True) # 启用自动换行
        self.name_label.setAlignment(Qt.AlignCenter) # 文字居中对齐
        self.name_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #34495e;
                font-weight: bold;
            }
        """)
        text_layout.addWidget(self.name_label, alignment=Qt.AlignTop)

        self.status_label = QLabel("等待数据...")
        self.status_label.setWordWrap(True) # 启用自动换行
        self.status_label.setAlignment(Qt.AlignCenter) # 文字居中对齐
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
            }
        """)
        text_layout.addWidget(self.status_label, alignment=Qt.AlignVCenter) # Vertically center

        self.update_time_label = QLabel("")
        self.update_time_label.setAlignment(Qt.AlignCenter) # 文字居中对齐
        self.update_time_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #7f8c8d;
            }
        """)
        text_layout.addWidget(self.update_time_label, alignment=Qt.AlignBottom)
        
        text_layout.addStretch() # Push time to bottom if space

        main_layout.addWidget(text_widget)
        main_layout.setStretchFactor(text_widget, 4) # 增加文本部分所占比例

    def update_status(self, status, value=""):
        self.status = status
        self.value = value
        current_time_str = QDateTime.currentDateTime().toString("HH:mm:ss")

        status_text = "正常" if status else "异常"
        status_color = "#2ecc71" if status else "#e74c3c"
        
        self.status_label.setText(status_text)
            
        self.status_label.setStyleSheet(f"""
            QLabel {{
                font-size: 18px;
                color: {status_color};
                font-weight: bold;
            }}
        """)
        self.update_time_label.setText(f"更新: {current_time_str}")
        self.update()

class TopBarWidget(QFrame):
    """Widget for the top bar including title, device selection, and buttons."""
    def __init__(self, parent_widget=None): # parent_widget is the VehicleStatusWidget
        super().__init__(parent_widget)
        self.parent_widget = parent_widget
        self.setFixedHeight(60)
        self.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa; /* Light grey background */
                border-bottom: 1px solid #dee2e6; /* Subtle border */
                padding: 0 15px;
            }
        """)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0,0,0,0)
        layout.setSpacing(15)

        title = QLabel("状态监控系统")
        title.setStyleSheet("""
            QLabel {
                font-size: 20px; /* Slightly reduced for top bar */
                font-weight: bold;
                color: #343a40; /* Darker title color */
            }
        """)
        layout.addWidget(title)
        layout.addSpacerItem(QSpacerItem(20, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))

        layout.addWidget(QLabel("选择设备："))
        self.device_selector = QComboBox()
        self.device_selector.setMinimumWidth(220)
        self.device_selector.setStyleSheet("""
            QComboBox {
                padding: 8px 12px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
                font-size: 13px;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left-width: 1px;
                border-left-color: #ced4da;
                border-left-style: solid;
                border-top-right-radius: 3px;
                border-bottom-right-radius: 3px;
            }
            QComboBox::down-arrow {
                image: url(./assets/down_arrow.png); /* Make sure this path is correct */
            }
        """)
        self.device_selector.currentIndexChanged.connect(self.parent_widget.on_device_selected)
        layout.addWidget(self.device_selector)

        self.manage_btn = QPushButton(QIcon("./assets/settings.png"), "设备管理")
        self.manage_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        self.manage_btn.setIconSize(QSize(16,16))
        self.manage_btn.clicked.connect(self.parent_widget.show_device_manager_dialog) # Connect to parent's method
        layout.addWidget(self.manage_btn)

        self.deploy_btn = QPushButton(QIcon("./assets/deploy.png"), "增加监控设备")
        self.deploy_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #0069d9;
            }
        """)
        self.deploy_btn.setIconSize(QSize(16,16))
        self.deploy_btn.clicked.connect(self.parent_widget.show_deploy_dialog) # Connect to parent's method
        layout.addWidget(self.deploy_btn)

    def refresh_device_list_in_selector(self, devices):
        current_data = self.device_selector.currentData()
        self.device_selector.blockSignals(True)
        self.device_selector.clear()
        self.device_selector.addItem("请选择设备...", None)
        selected_index = 0
        for i, device in enumerate(devices):
            self.device_selector.addItem(device['vin'], device)
            if current_data and device['vin'] == current_data['vin']:
                selected_index = i + 1
        self.device_selector.setCurrentIndex(selected_index)
        self.device_selector.blockSignals(False)


class VehicleStatusWidget(QWidget):
    status_updated = pyqtSignal(list)  # 发送状态更新信号
    connection_updated = pyqtSignal(bool, str)  # 发送连接状态更新信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.client_sockets = {}
        self.current_device = None
        self.current_vin = None # To store the VIN of the connected device
        self.last_data_time = {}
        self.init_ui()
        
        self.start_server()
        
        self.timeout_timer = QTimer(self)
        self.timeout_timer.timeout.connect(self.check_data_timeout)
        self.timeout_timer.start(1000)
        
    def init_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0,0,0,0) # Remove margins for top bar to span full width
        main_layout.setSpacing(0) # No spacing between top bar and content

        self.top_bar = TopBarWidget(self) # Pass self to TopBarWidget
        main_layout.addWidget(self.top_bar)

        # Content Area
        content_area = QWidget()
        content_layout = QVBoxLayout(content_area)
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(20)
        main_layout.addWidget(content_area)
        
        # Connection Status (below top bar, inside content area)
        self.conn_status_label = QLabel("请选择一个设备进行连接")
        self.conn_status_label.setAlignment(Qt.AlignCenter)
        self.conn_status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6c757d;
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 4px;
                min-height: 30px; /* Ensure it has some height */
            }
        """)
        content_layout.addWidget(self.conn_status_label)
        
        # 添加上部空白区域（10%）
        content_layout.addStretch(1)
        
        # Status Indicators Grid
        grid_widget = QWidget()
        grid_layout = QGridLayout(grid_widget)
        grid_layout.setSpacing(20) # 增加卡片之间的间距
        
        # 设置网格容器的大小策略
        grid_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        self.indicators = {}
        # Define device names and their corresponding icons
        self.device_info_map = {
            "组合惯导": {"icon": "../image/gnss.png"},
            "中间32线激光雷达": {"icon": "../image/ls32.png"},
            "左16线激光雷达": {"icon": "../image/ls16.png"},
            "右16线激光雷达": {"icon": "../image/ls16.png"},
            "线控底盘通信状态": {"icon": "../image/car.png"},
            "毫米波雷达": {"icon": "../image/radar.png"},
            "森云摄像头": {"icon": "../image/camera.png"},
            "电台": {"icon": "../image/radio.png"},
            "超声波雷达": {"icon": "../image/ultrasonic.png"}
        }
        
        # 设备顺序和布局（3列布局，共9个设备）
        device_layout = [
            ["中间32线激光雷达", "左16线激光雷达", "右16线激光雷达"],
            ["毫米波雷达", "超声波雷达", "线控底盘通信状态"],
            ["组合惯导", "电台", "森云摄像头"]
        ]
        
        # 创建所有设备指示器
        for row_idx, row_devices in enumerate(device_layout):
            for col_idx, device_name in enumerate(row_devices):
                icon_path = self.device_info_map[device_name]["icon"]
                indicator = StatusIndicator(device_name, icon_path)
                self.indicators[device_name] = indicator
                grid_layout.addWidget(indicator, row_idx, col_idx)
            
        # 设置行列伸缩比例，使网格均匀分布
        for i in range(len(device_layout)):  # 行数
            grid_layout.setRowStretch(i, 1)
        for i in range(3):  # 3列
            grid_layout.setColumnStretch(i, 1)
            
        content_layout.addWidget(grid_widget)
        # 添加下部空白区域（10%）
        content_layout.addStretch(1)
        
        self.status_updated.connect(self.update_status_indicators)
        self.connection_updated.connect(self.update_connection_display)
        
        self.refresh_device_list()

    def show_deploy_dialog(self):
        get_logger().log_user_action("打开OTA部署窗口")
        dialog = DeployDialog(self)
        dialog.exec_()
        
    def show_device_manager_dialog(self):
        get_logger().log_user_action("打开设备管理器")
        dialog = DeviceManagerDialog(self)
        # Connect the signal from the dialog to refresh the device list
        dialog.devices_updated.connect(self.refresh_device_list)
        dialog.exec_()
        
    def refresh_device_list(self):
        devices = []
        if os.path.exists("devices.json"):
            try:
                with open("devices.json", "r", encoding="utf-8") as f:
                    devices = json.load(f)
            except Exception as e:
                print(f"加载设备列表失败: {e}")
                # Ensure file exists even if loading fails or it's empty/corrupt
                if not os.path.exists("devices.json"):
                    with open("devices.json", "w", encoding="utf-8") as f:
                        json.dump([], f)
        else: # If devices.json doesn't exist at all
            with open("devices.json", "w", encoding="utf-8") as f:
                json.dump([], f)
        
        self.top_bar.refresh_device_list_in_selector(devices)
        if not devices:
            self.conn_status_label.setText("无可用设备。请通过'设备管理'添加设备。")
            self.conn_status_label.setStyleSheet("QLabel { font-size: 14px; color: #dc3545; padding: 10px; background-color: #f8d7da; border-radius: 4px; min-height:30px; }")
            for indicator_name in self.indicators: # Reset indicators by name
                self.indicators[indicator_name].update_status(False, "")
                self.indicators[indicator_name].status_label.setText("等待连接")
            self.current_vin = None # Clear VIN when no devices
        else:
            self.conn_status_label.setText("请选择一个设备以查看状态")

    def on_device_selected(self, index):
        """Handles device selection from the dropdown."""
        device = self.top_bar.device_selector.itemData(index)
        
        # Log the action only if a valid device is selected
        if device:
            get_logger().log_user_action(f"选择监控设备: {device.get('vin', '未知VIN')}")

        self.current_device = device

        if device:
            # If we already have a connection from this VIN, update status immediately
            if device['vin'] in self.client_sockets:
                try:
                    peer_addr = self.client_sockets[device['vin']].getpeername()
                    self.connection_updated.emit(True, f"从 {peer_addr[0]}:{peer_addr[1]} 接收数据")
                except OSError: # Socket might be closed already
                     self.update_connection_display(False, f"等待来自 {device.get('vin')} 的数据...")
            else:
                self.update_connection_display(False, f"等待来自 {device.get('vin')} 的数据...")
                # 重置所有指示器状态为等待数据
                for indicator in self.indicators.values():
                    indicator.update_status(False, "")
                    indicator.status_label.setText("等待数据")
        else:
            # If "Please select" is chosen
            self.update_connection_display(False, "未选择设备")
            self.last_data_time.clear() # Reset timeout timer
            for indicator in self.indicators.values():
                indicator.update_status(False, "N/A")

    def start_server(self):
        """Starts the TCP server in a background thread."""
        server_thread = Thread(target=self._server_loop, daemon=True)
        server_thread.start()

    def _server_loop(self):
        """The main loop for the TCP server to accept connections."""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                s.bind(('0.0.0.0', 5488))
                s.listen(5)
                get_logger().log_event("INFO", "监控服务器已在端口 5488 启动，等待远程设备连接。")
                while True:
                    client_socket, addr = s.accept()
                    get_logger().log_event("INFO", f"接受到来自 {addr} 的新连接。")
                    client_handler_thread = Thread(target=self._handle_client, args=(client_socket, addr), daemon=True)
                    client_handler_thread.start()
        except Exception as e:
            get_logger().log_event("ERROR", f"服务器未能启动或运行时发生错误: {e}")
            self.connection_updated.emit(False, f"服务器错误: {e}")

    def _handle_client(self, client_socket, addr):
        """Handles an individual client connection."""
        vin = None
        try:
            while True:
                data = client_socket.recv(1024).decode().strip()
                if not data:
                    break
                
                parts = data.split(',')
                vin_index = -1
                if len(parts) == 10:  # 9个状态值 + VIN
                    vin_index = 9
                elif len(parts) == 9:
                    vin_index = 8
                elif len(parts) == 5:
                    vin_index = 4

                if vin_index != -1:
                    vin = parts[vin_index]
                    # Store client socket and update data timestamp
                    self.client_sockets[vin] = client_socket
                    self.last_data_time[vin] = time.time()
                    
                    # If this client corresponds to the currently selected device, update UI
                    if self.current_device and vin == self.current_device.get('vin'):
                        self.status_updated.emit(parts)
                        # We are connected and receiving data
                        self.connection_updated.emit(True, f"从 {addr[0]}:{addr[1]} 接收数据")
        
        except (ConnectionResetError, BrokenPipeError, UnicodeDecodeError) as e:
            get_logger().log_event("WARNING", f"与 {addr} 的连接丢失: {e}")
        except Exception as e:
            get_logger().log_event("ERROR", f"处理客户端 {addr} 数据时发生未知错误: {e}")
        finally:
            if vin:
                get_logger().log_event("INFO", f"设备 {vin} ({addr}) 已断开连接。")
                if vin in self.client_sockets:
                    del self.client_sockets[vin]
                if vin in self.last_data_time:
                    del self.last_data_time[vin]

                # If the disconnected client was the one being monitored, update UI
                if self.current_device and vin == self.current_device.get('vin'):
                    self.connection_updated.emit(False, f"设备 {vin} 连接已断开")
                    # 设备断开时，将所有状态指示器设置为异常状态
                    for indicator in self.indicators.values():
                        indicator.update_status(False, "")
                        indicator.status_label.setText("连接断开")

            client_socket.close()
            
    def update_status_indicators(self, parts):
        # parts: [serial_active, mid_32_active, left_16_active, right_16_active, can0_active, can1_active, camera_active, radio_active, ultrasonic_active, vin]

        # Store the VIN
        if len(parts) == 10:  # 9个状态值 + VIN
            self.current_vin = parts[9]
        elif len(parts) == 9:  # 兼容旧格式
            self.current_vin = parts[8]
            # If connection is already established, update the display text to include VIN
            if self.current_device and self.current_device['vin'] in self.client_sockets:
                 # Get current connection info (IP:Port) to preserve it
                client_ip_port_info = ""
                current_text = self.conn_status_label.text()
                if "(" in current_text and ")" in current_text: # Try to extract existing IP:Port
                    client_ip_port_info = current_text[current_text.find("(")+1:current_text.find(")")]
                    if "VIN:" in client_ip_port_info: # if VIN was already there, extract just IP:Port
                         client_ip_port_info = client_ip_port_info.split(", IP:")[1].strip() if ", IP:" in client_ip_port_info else client_ip_port_info.split("IP:")[1].strip()


                self.update_connection_display(True, client_ip_port_info if client_ip_port_info else "")

        # 设备名称与parts索引的映射关系
        # 保持原有的数据索引映射不变，只是调整了显示布局
        device_mapping = {
            "组合惯导": 0,             # serial_active
            "中间32线激光雷达": 1,      # mid_32_active
            "左16线激光雷达": 2,        # left_16_active
            "右16线激光雷达": 3,        # right_16_active
            "线控底盘通信状态": 4,      # can0_active
            "毫米波雷达": 5,            # can1_active
            "森云摄像头": 6,            # camera_active
            "电台": 7,                 # radio_active
            "超声波雷达": 8             # ultrasonic_active
        }

        for name, index in device_mapping.items():
            if name in self.indicators:
                if index < len(parts) - 1:  # 最后一个元素是VIN码，不用于状态指示
                    is_active = parts[index] == '1'
                    value_to_display = parts[index]  # Could be '0' or '1'
                    self.indicators[name].update_status(is_active, value_to_display)
                else:
                    # 如果索引超出范围，将状态设置为异常
                    self.indicators[name].update_status(False, "数据不可用")

    def update_connection_display(self, connected, info=""):
        if connected:
            base_text = f"已连接到 {self.current_device['vin']}"
            details = []
            if self.current_vin and self.current_vin != "None": # Check if VIN is available and not "None"
                details.append(f"设备VIN: {self.current_vin}")
            if info: # IP:Port
                details.append(f"IP: {info}")
            
            if details:
                base_text += f" ({', '.join(details)})"
            
            self.conn_status_label.setText(base_text)
            self.conn_status_label.setStyleSheet("QLabel { font-size: 14px; color: #28a745; padding: 15px; background-color: #d4edda; border-radius: 4px; min-height:30px; }")
        else:
            self.current_vin = None # Clear VIN when not connected
            if self.current_device:
                 self.conn_status_label.setText(f"与 {self.current_device['vin']} 连接失败: {info}")
                 self.conn_status_label.setStyleSheet("QLabel { font-size: 14px; color: #dc3545; padding: 15px; background-color: #f8d7da; border-radius: 4px; min-height:30px; }")
            else:
                self.conn_status_label.setText("请选择一个设备进行连接")
                self.conn_status_label.setStyleSheet("QLabel { font-size: 14px; color: #6c757d; padding: 15px; background-color: #f8f9fa; border-radius: 4px; min-height:30px; }")

    def check_data_timeout(self):
        current_time = time.time()
        # Only check timeout for the currently selected device
        if self.current_device:
            vin = self.current_device['vin']
            # Check if we have received data for this vin and if it's stale
            if vin in self.last_data_time:
                if current_time - self.last_data_time[vin] > 3:
                    get_logger().log_event("WARNING", f"设备 {vin} 数据接收超时。")
                    # Set status to abnormal for all indicators
                    for indicator in self.indicators.values():
                        indicator.update_status(False, "")
                        indicator.status_label.setText("数据超时")

                    self.connection_updated.emit(False, "数据超时")

                    # Clean up stale connection info
                    if vin in self.last_data_time:
                        del self.last_data_time[vin]
                    if vin in self.client_sockets:
                        try:
                            self.client_sockets[vin].close()
                        except Exception:
                            pass # Ignore errors on close, it might be closed already
            else:
                # 如果当前选择的设备没有数据记录，说明从未连接或已断开
                # 确保所有指示器显示为等待连接状态
                for indicator in self.indicators.values():
                    if indicator.status_label.text() not in ["等待连接", "等待数据"]:
                        indicator.update_status(False, "")
                        indicator.status_label.setText("等待连接")
            